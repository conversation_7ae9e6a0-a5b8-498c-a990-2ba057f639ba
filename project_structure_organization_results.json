{"organized_files": 0, "created_directories": 0, "moved_files": 44, "updated_imports": 0, "errors": [], "init_files_created": 0, "file_statistics": {"python_files": 78, "json_files": 27, "shell_scripts": 59, "log_files": 4, "total_files": 193}, "naming_violations": 2, "structure_report": {"project_name": "starbucks_bypass_tester", "organization_date": "2025-07-31", "structure": {"complete_service_fix.sh": "文件 (8500 bytes)", "diagnose_and_fix_service.sh": "文件 (6438 bytes)", "FINAL_ORGANIZATION_STATUS.md": "文件 (4500 bytes)", "fix_indentation_errors.sh": "文件 (8100 bytes)", "fix_relative_imports.sh": "文件 (4751 bytes)", "fix_script_execution.sh": "文件 (1547 bytes)", "PROJECT_STRUCTURE.md": "文件 (8148 bytes)", "quick_fix_install.sh": "文件 (2215 bytes)", "README.md": "文件 (2809 bytes)", "restore_complete_system.sh": "文件 (7517 bytes)", "run_all_tests.sh": "文件 (6806 bytes)", "scripts/": {"deployment/": {"create_user.sh": "文件 (9780 bytes)", "delete_user.sh": "文件 (14228 bytes)", "install_ubuntu.sh": "文件 (15529 bytes)", "server_install_psutil_fix.sh": "文件 (8464 bytes)", "uninstall_ubuntu.sh": "文件 (12344 bytes)"}, "fixes/": {"emergency_server_fix.sh": "文件 (10382 bytes)", "fix_api_initialization_issue9.sh": "文件 (5935 bytes)", "fix_api_service_server.sh": "文件 (7003 bytes)", "fix_bypass_engine_init.sh": "文件 (6927 bytes)", "fix_indentation.py": "文件 (4348 bytes)", "fix_line_208_simple.sh": "文件 (4843 bytes)", "fix_service_initialization.sh": "文件 (4814 bytes)", "quick_fix_208.sh": "文件 (922 bytes)", "server_fix_api_final.sh": "文件 (4505 bytes)", "server_fix_api_initialization.sh": "文件 (9251 bytes)", "server_fix_startup_issue.sh": "文件 (7635 bytes)", "server_fix_yaml_dependency.sh": "文件 (9438 bytes)"}, "maintenance/": {"clean_and_organize_project.py": "文件 (11610 bytes)", "organize_project_structure.py": "文件 (14176 bytes)", "server_complete_fix.sh": "文件 (7312 bytes)", "server_complete_import_fix.sh": "文件 (10230 bytes)", "server_complete_rebuild.sh": "文件 (7760 bytes)", "server_complete_repair.sh": "文件 (8122 bytes)", "server_correct_fix.sh": "文件 (21319 bytes)", "server_correct_import_fix.sh": "文件 (7533 bytes)", "server_debug_startup_failure.sh": "文件 (5107 bytes)", "server_direct_fix.sh": "文件 (5466 bytes)", "server_final_absolute_fix.sh": "文件 (23981 bytes)", "server_final_complete_fix.sh": "文件 (6753 bytes)", "server_final_fix.sh": "文件 (6081 bytes)", "server_precise_import_fix.sh": "文件 (8969 bytes)", "server_rebuild_files.sh": "文件 (9835 bytes)", "server_repair_safe.sh": "文件 (6143 bytes)", "server_simple_import_fix.sh": "文件 (6702 bytes)"}, "management/": {"check_status.sh": "文件 (8814 bytes)", "monitor.sh": "文件 (8265 bytes)", "start_all.sh": "文件 (5272 bytes)", "view_logs.sh": "文件 (11177 bytes)"}, "testing/": {"api_test_suite.py": "文件 (13823 bytes)", "quick_api_test.py": "文件 (7850 bytes)", "test_api_after_fix.sh": "文件 (3432 bytes)", "verify_api_models.py": "文件 (4473 bytes)", "verify_bypass_engine_fix.py": "文件 (5889 bytes)", "verify_fixes_guide4.py": "文件 (8122 bytes)"}, "utils/": {"code_standards_checker.py": "文件 (14272 bytes)", "diagnose_single_bypass.py": "文件 (6954 bytes)", "final_verification.py": "文件 (6753 bytes)"}}, "server_fix_commands.sh": "文件 (3602 bytes)", "simple_fix_and_start.sh": "文件 (8195 bytes)", "starbucks_bypass_tester/": {"data/": {"analysis/": {}, "processed/": {}, "raw/": {}, "results/": {}}, "logs/": {"application.json": "文件 (2827408 bytes)", "application.log": "文件 (1741938 bytes)", "error.log": "文件 (16182 bytes)"}, "main.py": "文件 (9122 bytes)", "project_organization_results.json": "文件 (4529 bytes)", "requirements.txt": "文件 (478 bytes)", "src/": {"__init__.py": "文件 (166 bytes)", "cli/": {}, "config/": {}, "core/": {}, "utils/": {}}, "test_api.sh": "文件 (9494 bytes)", "tests/": {"__init__.py": "文件 (211 bytes)", "test_basic_functionality.py": "文件 (9917 bytes)", "test_bypass_engine.py": "文件 (33160 bytes)", "test_concurrency.py": "文件 (29271 bytes)", "test_device_fingerprint.py": "文件 (20829 bytes)", "test_device_manager.py": "文件 (10122 bytes)", "test_header_generator.py": "文件 (10832 bytes)", "test_integration.py": "文件 (27707 bytes)", "test_time_scheduler.py": "文件 (4443 bytes)"}}, "xbkk/": {"run_all_tests.sh": "文件 (6806 bytes)", "scripts/": {"api_test_suite.py": "文件 (13823 bytes)", "check_status.sh": "文件 (8814 bytes)", "create_user.sh": "文件 (9780 bytes)", "delete_user.sh": "文件 (14228 bytes)", "fixes/": {}, "install_ubuntu.sh": "文件 (15462 bytes)", "monitor.sh": "文件 (8265 bytes)", "quick_api_test.py": "文件 (7850 bytes)", "start_all.sh": "文件 (5272 bytes)", "uninstall_ubuntu.sh": "文件 (12344 bytes)", "view_logs.sh": "文件 (11177 bytes)"}, "starbucks_bypass_tester/": {"data/": {}, "logs/": {}, "main.py": "文件 (7772 bytes)", "requirements.txt": "文件 (478 bytes)", "src/": {}, "test_api.sh": "文件 (9494 bytes)", "tests/": {}}}, "xbkk.zip": "文件 (3007392 bytes)", "安装问题9.md": "文件 (12643 bytes)", "星巴克app设备指纹风控绕过.txt": "文件 (3475710 bytes)", "项目代码整理报告.md": "文件 (12916 bytes)", "项目整理完成确认.md": "文件 (6609 bytes)", "项目结构整理方案.md": "文件 (12284 bytes)"}, "statistics": {"python_files": 78, "json_files": 27, "shell_scripts": 59, "log_files": 4, "total_files": 193}, "compliance": {"naming_violations": 2, "structure_complete": true, "init_files_present": true}}}