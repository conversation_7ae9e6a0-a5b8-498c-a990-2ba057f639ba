#!/bin/bash

# 星巴克设备指纹绕过系统 - 一键启动脚本
# 用途: 快速启动所有系统服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  星巴克设备指纹绕过系统 - 一键启动${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查虚拟环境
echo -e "${YELLOW}[步骤1/5]${NC} 检查Python虚拟环境..."
if [ ! -d "venv" ]; then
    echo -e "${RED}错误: 虚拟环境不存在，请先运行安装脚本${NC}"
    echo "运行: ./scripts/install_ubuntu.sh"
    exit 1
fi

echo -e "${GREEN}✓ 虚拟环境存在${NC}"

# 激活虚拟环境
echo -e "${YELLOW}[步骤2/5]${NC} 激活虚拟环境..."
source venv/bin/activate
echo -e "${GREEN}✓ 虚拟环境已激活${NC}"

# 检查依赖
echo -e "${YELLOW}[步骤3/5]${NC} 检查Python依赖..."
if ! python -c "import fastapi, uvicorn, aiohttp" 2>/dev/null; then
    echo -e "${RED}错误: Python依赖不完整，正在安装...${NC}"
    pip install -r requirements.txt
fi
echo -e "${GREEN}✓ Python依赖完整${NC}"

# 创建日志目录
echo -e "${YELLOW}[步骤4/5]${NC} 准备日志目录..."
mkdir -p logs
echo -e "${GREEN}✓ 日志目录已准备${NC}"

# 检查端口是否被占用
echo -e "${YELLOW}[步骤5/5]${NC} 检查端口8000..."
if netstat -tlnp 2>/dev/null | grep ":8000 " > /dev/null; then
    echo -e "${YELLOW}警告: 端口8000已被占用，尝试停止现有服务...${NC}"
    
    # 尝试找到并停止占用端口的进程
    local pid=$(netstat -tlnp 2>/dev/null | grep ":8000 " | awk '{print $7}' | cut -d'/' -f1 | head -1)
    if [ -n "$pid" ] && [ "$pid" != "-" ]; then
        echo "正在停止进程 $pid..."
        kill -TERM "$pid" 2>/dev/null || true
        sleep 2
        
        # 如果进程仍在运行，强制杀死
        if kill -0 "$pid" 2>/dev/null; then
            echo "强制停止进程 $pid..."
            kill -KILL "$pid" 2>/dev/null || true
        fi
    fi
    
    # 再次检查端口
    if netstat -tlnp 2>/dev/null | grep ":8000 " > /dev/null; then
        echo -e "${RED}错误: 无法释放端口8000，请手动停止占用进程${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✓ 端口8000可用${NC}"
echo ""

# 启动API服务
echo -e "${BLUE}启动API服务...${NC}"
echo -e "${YELLOW}[信息]${NC} 启动命令: python main.py server --port 8000"
echo -e "${YELLOW}[信息]${NC} 日志文件: logs/application.log"
echo -e "${YELLOW}[信息]${NC} 按 Ctrl+C 停止服务"
echo ""

# 启动服务（前台运行）
python main.py server --port 8000 &
SERVER_PID=$!

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 3

# 检查服务是否成功启动
if kill -0 $SERVER_PID 2>/dev/null; then
    echo -e "${GREEN}✓ API服务启动成功 (PID: $SERVER_PID)${NC}"
    
    # 等待服务完全就绪
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            echo -e "${GREEN}✓ API服务就绪${NC}"
            break
        fi
        
        echo "等待API服务就绪... ($attempt/$max_attempts)"
        sleep 1
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        echo -e "${RED}警告: API服务可能未完全就绪${NC}"
    fi
else
    echo -e "${RED}错误: API服务启动失败${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  服务启动完成${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 显示服务信息
echo -e "${GREEN}[庆祝] 所有服务启动成功！${NC}"
echo ""
echo -e "${YELLOW}服务信息:${NC}"
echo "  • API服务地址: http://localhost:8000"
echo "  • API文档: http://localhost:8000/docs"
echo "  • ReDoc文档: http://localhost:8000/redoc"
echo "  • 健康检查: http://localhost:8000/health"
echo ""

echo -e "${YELLOW}快速测试:${NC}"
echo "  curl http://localhost:8000/health"
echo "  curl http://localhost:8000/devices"
echo ""

echo -e "${YELLOW}监控命令:${NC}"
echo "  ./scripts/check_status.sh    # 检查系统状态"
echo "  ./scripts/monitor.sh         # 实时监控"
echo "  ./scripts/view_logs.sh       # 查看日志"
echo ""

echo -e "${YELLOW}停止服务:${NC}"
echo "  按 Ctrl+C 停止当前服务"
echo "  或运行: kill $SERVER_PID"
echo ""

# 保存PID到文件
echo $SERVER_PID > logs/server.pid
echo -e "${YELLOW}[信息]${NC} 服务PID已保存到 logs/server.pid"

# 等待用户中断
trap 'echo -e "\n${YELLOW}正在停止服务...${NC}"; kill $SERVER_PID 2>/dev/null; wait $SERVER_PID 2>/dev/null; echo -e "${GREEN}服务已停止${NC}"; exit 0' SIGINT SIGTERM

echo -e "${CYAN}服务正在运行中... 按 Ctrl+C 停止${NC}"

# 等待服务进程
wait $SERVER_PID
