#!/bin/bash

# 紧急服务器修复脚本 - 解决重复替换问题
echo "🚨 紧急修复服务器代码损坏问题..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 检查并恢复备份
echo "📦 检查备份文件..."
if [ -f "src/core/api_service.py.backup" ]; then
    echo "恢复 api_service.py 从备份..."
    cp src/core/api_service.py.backup src/core/api_service.py
else
    echo "❌ 没有找到 api_service.py.backup"
fi

if [ -f "src/core/bypass_engine.py.backup" ]; then
    echo "恢复 bypass_engine.py 从备份..."
    cp src/core/bypass_engine.py.backup src/core/bypass_engine.py
else
    echo "❌ 没有找到 bypass_engine.py.backup"
fi

if [ -f "src/core/device_fingerprint_engine.py.backup" ]; then
    echo "恢复 device_fingerprint_engine.py 从备份..."
    cp src/core/device_fingerprint_engine.py.backup src/core/device_fingerprint_engine.py
else
    echo "❌ 没有找到 device_fingerprint_engine.py.backup"
fi

# 3. 创建完整的修复脚本
echo "🔧 创建完整修复脚本..."
cat > complete_fix.py << 'PYTHON_SCRIPT_END'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def read_file(filepath):
    """安全读取文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"❌ 读取文件失败 {filepath}: {e}")
        return None

def write_file(filepath, content):
    """安全写入文件"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"❌ 写入文件失败 {filepath}: {e}")
        return False

def fix_bypass_engine():
    """修复 bypass_engine.py"""
    filepath = 'src/core/bypass_engine.py'
    print(f"🔧 修复 {filepath}...")
    
    content = read_file(filepath)
    if content is None:
        return False
    
    original_content = content
    
    # 修复1: DeviceFingerprintEngine 类型注解
    if "fingerprint_engine: DeviceFingerprintEngine = None" in content:
        content = content.replace(
            "fingerprint_engine: DeviceFingerprintEngine = None",
            "fingerprint_engine: 'DeviceFingerprintEngine' = None"
        )
        print("  ✅ 修复 DeviceFingerprintEngine 类型注解")
    
    # 修复2: DeviceFingerprint 类型注解
    if "fingerprint: DeviceFingerprint) -> float:" in content:
        content = content.replace(
            "fingerprint: DeviceFingerprint) -> float:",
            "fingerprint: 'DeviceFingerprint') -> float:"
        )
        print("  ✅ 修复 DeviceFingerprint 类型注解")
    
    if content != original_content:
        return write_file(filepath, content)
    else:
        print("  ℹ️ 无需修复")
        return True

def fix_api_service():
    """修复 api_service.py"""
    filepath = 'src/core/api_service.py'
    print(f"🔧 修复 {filepath}...")
    
    content = read_file(filepath)
    if content is None:
        return False
    
    original_content = content
    
    # 修复1: 方法名
    if "self.bypass_engine.get_bypass_stats()" in content:
        content = content.replace(
            "self.bypass_engine.get_bypass_stats()",
            "await self.bypass_engine.get_bypass_statistics()"
        )
        print("  ✅ 修复方法名 get_bypass_stats -> get_bypass_statistics")
    
    # 修复2: 参数传递 - 逐行处理避免重复替换
    lines = content.split('\n')
    modified = False
    
    for i, line in enumerate(lines):
        # 只修复没有 data= 前缀的行
        if "request.data," in line and "data=request.data," not in line:
            lines[i] = line.replace("request.data,", "data=request.data,")
            print(f"  ✅ 修复第{i+1}行: request.data 参数")
            modified = True
        
        if "bypass_req.data," in line and "data=bypass_req.data," not in line:
            lines[i] = line.replace("bypass_req.data,", "data=bypass_req.data,")
            print(f"  ✅ 修复第{i+1}行: bypass_req.data 参数")
            modified = True
        
        # 修复 strategy 参数
        if line.strip() == "strategy" and i > 0:
            # 检查前一行是否已经有 strategy=
            prev_line = lines[i-1] if i > 0 else ""
            if "strategy=" not in prev_line:
                lines[i] = line.replace("strategy", "strategy=strategy")
                print(f"  ✅ 修复第{i+1}行: strategy 参数")
                modified = True
    
    if modified:
        content = '\n'.join(lines)
    
    # 修复3: 返回值访问 - 只替换没有被替换过的
    replacements = [
        ("result.success", "result['success']"),
        ("result.risk_level.value", "result['risk_level']"),
        ("result.confidence", "result['confidence']"),
        ("result.fingerprint_quality", "result['fingerprint_quality']"),
        ("result.bypass_techniques", "result['bypass_techniques']"),
        ("result.warnings", "result['warnings']"),
        ("result.execution_time", "result['execution_time']"),
        ("result.timestamp.isoformat()", "result['timestamp']"),
    ]
    
    for old_pattern, new_pattern in replacements:
        if old_pattern in content and new_pattern not in content:
            content = content.replace(old_pattern, new_pattern)
            print(f"  ✅ 修复返回值访问: {old_pattern} -> {new_pattern}")
    
    if content != original_content:
        return write_file(filepath, content)
    else:
        print("  ℹ️ 无需修复")
        return True

def fix_device_fingerprint():
    """修复 device_fingerprint_engine.py"""
    filepath = 'src/core/device_fingerprint_engine.py'
    print(f"🔧 修复 {filepath}...")
    
    content = read_file(filepath)
    if content is None:
        return False
    
    original_content = content
    
    # 修复1: 添加 success_rate 字段
    if 'success_rate: float = 1.0' not in content:
        target_line = 'additional_headers: Optional[Dict[str, str]] = None  # 添加additional_headers参数以兼容测试'
        if target_line in content:
            content = content.replace(
                target_line,
                target_line + '\n    success_rate: float = 1.0  # 添加成功率字段'
            )
            print("  ✅ 添加 success_rate 字段")
    
    # 修复2: 添加方法
    if 'def is_available(self) -> bool:' not in content:
        methods_code = '''
    
    def is_available(self) -> bool:
        """检查设备指纹是否可用"""
        if not self.is_active:
            return False
        
        # 检查冷却时间
        if self.last_used:
            from datetime import timedelta
            cooldown_minutes = 5  # 默认冷却时间5分钟
            cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
            if datetime.now() < cooldown_end:
                return False
        
        return True
    
    def get_cooldown_remaining(self) -> float:
        """获取剩余冷却时间（分钟）"""
        if not self.last_used:
            return 0.0
        
        from datetime import timedelta
        cooldown_minutes = 5  # 默认冷却时间5分钟
        cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
        now = datetime.now()
        
        if now >= cooldown_end:
            return 0.0
        
        remaining = cooldown_end - now
        return remaining.total_seconds() / 60.0  # 转换为分钟'''
        
        # 在 success_rate 字段后添加方法
        target_line = 'success_rate: float = 1.0  # 添加成功率字段'
        if target_line in content:
            content = content.replace(target_line, target_line + methods_code)
            print("  ✅ 添加 is_available 和 get_cooldown_remaining 方法")
    
    if content != original_content:
        return write_file(filepath, content)
    else:
        print("  ℹ️ 无需修复")
        return True

def verify_syntax():
    """验证语法"""
    print("🔍 验证语法...")
    
    files_to_check = [
        'src/core/bypass_engine.py',
        'src/core/api_service.py',
        'src/core/device_fingerprint_engine.py'
    ]
    
    all_ok = True
    for filepath in files_to_check:
        result = os.system(f'python3 -m py_compile {filepath}')
        if result == 0:
            print(f"  ✅ {filepath} 语法正确")
        else:
            print(f"  ❌ {filepath} 语法错误")
            all_ok = False
    
    return all_ok

def main():
    """主函数"""
    print("🚀 开始完整修复...")
    
    success = True
    
    # 执行修复
    if not fix_bypass_engine():
        success = False
    
    if not fix_api_service():
        success = False
    
    if not fix_device_fingerprint():
        success = False
    
    # 验证语法
    if success and verify_syntax():
        print("🎉 所有修复完成且语法正确！")
        return True
    else:
        print("❌ 修复失败或语法错误")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
PYTHON_SCRIPT_END

# 4. 运行完整修复
echo "🔧 运行完整修复..."
python3 complete_fix.py

# 检查修复结果
if [ $? -eq 0 ]; then
    echo "✅ 修复成功"
else
    echo "❌ 修复失败"
    exit 1
fi

# 5. 清理临时文件
rm -f complete_fix.py safe_repair.py

# 6. 重启服务
echo "🚀 重启服务..."
sudo supervisorctl start starbucks_bypass

# 7. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 8. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 9. 测试API
echo "🧪 测试API..."
echo "健康检查:"
curl -s http://localhost:8000/health

echo -e "\n统计信息:"
curl -s http://localhost:8000/stats | jq '.' || curl -s http://localhost:8000/stats

echo -e "\n设备列表:"
curl -s http://localhost:8000/devices | jq '.' || curl -s http://localhost:8000/devices

echo -e "\n单次绕过测试:"
curl -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' | jq '.' || echo "单次绕过测试完成"

echo -e "\n🎉 紧急修复完成！"
