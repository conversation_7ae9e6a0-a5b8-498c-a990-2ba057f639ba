#!/bin/bash

# 星巴克设备指纹绕过系统 - 状态检查脚本
# 用途: 快速检查系统各组件运行状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  星巴克设备指纹绕过系统 - 状态检查${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查函数
check_status() {
    local service_name="$1"
    local check_command="$2"
    local expected_result="$3"
    
    echo -n -e "${YELLOW}[检查]${NC} $service_name: "
    
    if eval "$check_command" > /dev/null 2>&1; then
        echo -e "${GREEN}正常 ✓${NC}"
        return 0
    else
        echo -e "${RED}异常 ✗${NC}"
        return 1
    fi
}

# 检查计数器
total_checks=0
passed_checks=0

# 1. 检查Python环境
echo -e "${BLUE}[1] Python环境检查${NC}"
total_checks=$((total_checks + 1))
if [ -d "venv" ]; then
    echo -n -e "${YELLOW}[检查]${NC} 虚拟环境: "
    echo -e "${GREEN}存在 ✓${NC}"
    passed_checks=$((passed_checks + 1))
    
    # 激活虚拟环境并检查依赖
    source venv/bin/activate
    total_checks=$((total_checks + 1))
    if python -c "import fastapi, uvicorn, aiohttp" 2>/dev/null; then
        echo -n -e "${YELLOW}[检查]${NC} Python依赖: "
        echo -e "${GREEN}完整 ✓${NC}"
        passed_checks=$((passed_checks + 1))
    else
        echo -n -e "${YELLOW}[检查]${NC} Python依赖: "
        echo -e "${RED}缺失 ✗${NC}"
    fi
else
    echo -n -e "${YELLOW}[检查]${NC} 虚拟环境: "
    echo -e "${RED}不存在 ✗${NC}"
fi

echo ""

# 2. 检查API服务
echo -e "${BLUE}[2] API服务检查${NC}"
total_checks=$((total_checks + 1))
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo -n -e "${YELLOW}[检查]${NC} API服务: "
    echo -e "${GREEN}运行中 ✓${NC}"
    passed_checks=$((passed_checks + 1))
    
    # 获取详细健康状态
    health_response=$(curl -s http://localhost:8000/health 2>/dev/null || echo '{}')
    if command -v jq > /dev/null 2>&1; then
        uptime=$(echo "$health_response" | jq -r '.uptime // "未知"')
        echo -e "${YELLOW}[信息]${NC} 运行时间: ${uptime}秒"
    fi
else
    echo -n -e "${YELLOW}[检查]${NC} API服务: "
    echo -e "${RED}未运行 ✗${NC}"
fi

echo ""

# 3. 检查设备池状态
echo -e "${BLUE}[3] 设备池检查${NC}"
total_checks=$((total_checks + 1))
if curl -s http://localhost:8000/devices > /dev/null 2>&1; then
    devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null || echo '[]')
    if command -v jq > /dev/null 2>&1; then
        device_count=$(echo "$devices_response" | jq '. | length')
        healthy_count=$(echo "$devices_response" | jq '[.[] | select(.is_healthy == true)] | length')
        
        echo -n -e "${YELLOW}[检查]${NC} 设备池状态: "
        if [ "$device_count" -ge 30 ] && [ "$healthy_count" -ge 25 ]; then
            echo -e "${GREEN}健康 ✓${NC}"
            passed_checks=$((passed_checks + 1))
        else
            echo -e "${YELLOW}警告 ⚠${NC}"
        fi
        
        echo -e "${YELLOW}[信息]${NC} 设备总数: $device_count"
        echo -e "${YELLOW}[信息]${NC} 健康设备: $healthy_count"
        
        if [ "$device_count" -gt 0 ]; then
            avg_success_rate=$(echo "$devices_response" | jq '[.[] | .success_rate] | add / length')
            echo -e "${YELLOW}[信息]${NC} 平均成功率: ${avg_success_rate}%"
        fi
    else
        echo -n -e "${YELLOW}[检查]${NC} 设备池状态: "
        echo -e "${GREEN}可访问 ✓${NC}"
        passed_checks=$((passed_checks + 1))
    fi
else
    echo -n -e "${YELLOW}[检查]${NC} 设备池状态: "
    echo -e "${RED}无法访问 ✗${NC}"
fi

echo ""

# 4. 检查系统资源
echo -e "${BLUE}[4] 系统资源检查${NC}"

# CPU使用率
total_checks=$((total_checks + 1))
if command -v top > /dev/null 2>&1; then
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}' 2>/dev/null || echo "0")
    echo -n -e "${YELLOW}[检查]${NC} CPU使用率: "
    if (( $(echo "$cpu_usage < 80" | bc -l 2>/dev/null || echo "1") )); then
        echo -e "${GREEN}${cpu_usage}% ✓${NC}"
        passed_checks=$((passed_checks + 1))
    else
        echo -e "${RED}${cpu_usage}% ✗${NC}"
    fi
else
    echo -n -e "${YELLOW}[检查]${NC} CPU使用率: "
    echo -e "${YELLOW}无法检测 ⚠${NC}"
fi

# 内存使用率
total_checks=$((total_checks + 1))
if command -v free > /dev/null 2>&1; then
    mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    echo -n -e "${YELLOW}[检查]${NC} 内存使用率: "
    if (( $(echo "$mem_usage < 85" | bc -l 2>/dev/null || echo "1") )); then
        echo -e "${GREEN}${mem_usage}% ✓${NC}"
        passed_checks=$((passed_checks + 1))
    else
        echo -e "${RED}${mem_usage}% ✗${NC}"
    fi
else
    echo -n -e "${YELLOW}[检查]${NC} 内存使用率: "
    echo -e "${YELLOW}无法检测 ⚠${NC}"
fi

# 磁盘空间
total_checks=$((total_checks + 1))
if command -v df > /dev/null 2>&1; then
    disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    echo -n -e "${YELLOW}[检查]${NC} 磁盘使用率: "
    if [ "$disk_usage" -lt 90 ]; then
        echo -e "${GREEN}${disk_usage}% ✓${NC}"
        passed_checks=$((passed_checks + 1))
    else
        echo -e "${RED}${disk_usage}% ✗${NC}"
    fi
else
    echo -n -e "${YELLOW}[检查]${NC} 磁盘使用率: "
    echo -e "${YELLOW}无法检测 ⚠${NC}"
fi

echo ""

# 5. 检查网络连接
echo -e "${BLUE}[5] 网络连接检查${NC}"
total_checks=$((total_checks + 1))
if curl -s --connect-timeout 5 https://httpbin.org/get > /dev/null 2>&1; then
    echo -n -e "${YELLOW}[检查]${NC} 外网连接: "
    echo -e "${GREEN}正常 ✓${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -n -e "${YELLOW}[检查]${NC} 外网连接: "
    echo -e "${RED}异常 ✗${NC}"
fi

# 检查端口监听
total_checks=$((total_checks + 1))
if netstat -tlnp 2>/dev/null | grep ":8000 " > /dev/null; then
    echo -n -e "${YELLOW}[检查]${NC} 端口8000监听: "
    echo -e "${GREEN}正常 ✓${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -n -e "${YELLOW}[检查]${NC} 端口8000监听: "
    echo -e "${RED}未监听 ✗${NC}"
fi

# 检查Nginx端口监听
total_checks=$((total_checks + 1))
if netstat -tlnp 2>/dev/null | grep ":8094 " > /dev/null; then
    echo -n -e "${YELLOW}[检查]${NC} 端口8094监听: "
    echo -e "${GREEN}正常 ✓${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -n -e "${YELLOW}[检查]${NC} 端口8094监听: "
    echo -e "${RED}未监听 ✗${NC}"
fi

echo ""

# 6. 检查日志文件
echo -e "${BLUE}[6] 日志文件检查${NC}"
total_checks=$((total_checks + 1))
if [ -f "logs/application.log" ]; then
    log_size=$(du -h logs/application.log | cut -f1)
    echo -n -e "${YELLOW}[检查]${NC} 应用日志: "
    echo -e "${GREEN}存在 (${log_size}) ✓${NC}"
    passed_checks=$((passed_checks + 1))
    
    # 检查最近的错误
    recent_errors=$(tail -100 logs/application.log | grep -i error | wc -l)
    if [ "$recent_errors" -gt 10 ]; then
        echo -e "${YELLOW}[警告]${NC} 最近100行日志中有 $recent_errors 个错误"
    fi
else
    echo -n -e "${YELLOW}[检查]${NC} 应用日志: "
    echo -e "${YELLOW}不存在 ⚠${NC}"
fi

echo ""

# 总结
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  检查结果总结${NC}"
echo -e "${BLUE}========================================${NC}"

success_rate=$(echo "scale=1; $passed_checks * 100 / $total_checks" | bc -l 2>/dev/null || echo "0")

echo -e "${YELLOW}总检查项目:${NC} $total_checks"
echo -e "${GREEN}通过项目:${NC} $passed_checks"
echo -e "${YELLOW}成功率:${NC} ${success_rate}%"

if [ "$passed_checks" -eq "$total_checks" ]; then
    echo ""
    echo -e "${GREEN}[庆祝] 所有检查项目通过！系统运行正常。${NC}"
    exit 0
elif [ "$passed_checks" -ge $((total_checks * 7 / 10)) ]; then
    echo ""
    echo -e "${YELLOW}[警告]  大部分检查项目通过，系统基本正常，建议关注异常项目。${NC}"
    exit 1
else
    echo ""
    echo -e "${RED}[错误] 多个检查项目失败，系统可能存在问题，请检查异常项目。${NC}"
    exit 2
fi
