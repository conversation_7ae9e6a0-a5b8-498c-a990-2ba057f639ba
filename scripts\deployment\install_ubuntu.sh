#!/bin/bash

# 星巴克设备指纹绕过系统 - Ubuntu快速安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量 - 固定用户名和密码
APP_USER="starbucks"
APP_PASSWORD="Starbucks@2025"
APP_DIR="/home/<USER>/apps/starbucks_bypass_tester"

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
    exit 1
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 检查Ubuntu版本
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_error "此脚本仅支持Ubuntu系统"
    fi
    
    # 检查Ubuntu版本号
    local version=$(lsb_release -rs)
    if (( $(echo "$version < 20.04" | bc -l) )); then
        log_warning "建议使用Ubuntu 20.04或更高版本"
    fi
    
    # 检查内存
    local memory=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    if [ "$memory" -lt 4 ]; then
        log_warning "系统内存少于4GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    local disk=$(df -h / | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "${disk%.*}" -lt 20 ]; then
        log_warning "可用磁盘空间少于20GB"
    fi
    
    log_success "系统检查完成"
}



# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."

    # 更新包列表 (不升级系统)
    sudo apt update

    # 安装Python和开发工具
    sudo apt install -y python3 python3-pip python3-venv python3-dev

    # 安装系统工具
    sudo apt install -y curl wget htop tree bc jq

    # 安装编译工具
    sudo apt install -y build-essential libssl-dev libffi-dev

    # 安装进程管理工具
    sudo apt install -y supervisor nginx

    log_success "系统依赖安装完成"
}

# 检查用户是否存在
check_user_exists() {
    log_info "检查应用用户: $APP_USER"

    # 检查用户是否已存在
    if ! id "$APP_USER" >/dev/null 2>&1; then
        log_error "用户 $APP_USER 不存在"
        echo ""
        echo "请先运行用户创建脚本:"
        echo "  ./scripts/create_user.sh"
        echo ""
        echo "然后使用 $APP_USER 用户运行此安装脚本:"
        echo "  su - $APP_USER"
        echo "  cd ~/starbucks_bypass_project"
        echo "  ./scripts/install_ubuntu.sh"
        echo ""
        exit 1
    fi

    log_success "用户 $APP_USER 已存在"

    # 显示用户信息
    echo ""
    echo "用户信息:"
    id "$APP_USER"
    echo ""

    # 验证用户权限
    if sudo -u "$APP_USER" sudo -n true 2>/dev/null; then
        log_success "用户 $APP_USER 具有sudo权限"
    else
        log_info "用户 $APP_USER 需要密码进行sudo操作"
    fi
}

# 部署应用
deploy_application() {
    log_info "部署应用..."

    # 创建应用目录
    sudo mkdir -p "$(dirname $APP_DIR)"
    sudo mkdir -p "$APP_DIR"
    sudo chown -R "$APP_USER:$APP_USER" "$(dirname $APP_DIR)"

    # 检查当前目录结构
    local current_dir=$(pwd)
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local project_root="$(dirname "$script_dir")"

    log_info "脚本目录: $script_dir"
    log_info "项目根目录: $project_root"

    # 检查项目结构 - 查找starbucks_bypass_tester目录
    local source_dir=""

    log_info "当前目录: $current_dir"
    log_info "脚本目录: $script_dir"
    log_info "项目根目录: $project_root"

    # 检查可能的项目位置
    local possible_locations=(
        "$project_root/starbucks_bypass_tester"  # 标准位置：scripts同级的starbucks_bypass_tester
        "$current_dir/starbucks_bypass_tester"   # 当前目录下的starbucks_bypass_tester
        "$current_dir"                           # 当前目录就是starbucks_bypass_tester
    )

    log_info "检查可能的starbucks_bypass_tester位置..."
    for location in "${possible_locations[@]}"; do
        echo "  检查: $location"
        if [ -d "$location" ] && [ -f "$location/main.py" ] && [ -d "$location/src" ]; then
            echo "    ✓ 找到完整的项目结构"
            source_dir="$location"
            break
        elif [ -f "$location/main.py" ] && [ -d "$location/src" ]; then
            echo "    ✓ 找到项目文件"
            source_dir="$location"
            break
        else
            echo "    ✗ 不是有效的项目目录"
            if [ -d "$location" ]; then
                echo "      main.py: $([ -f "$location/main.py" ] && echo "存在" || echo "不存在")"
                echo "      src目录: $([ -d "$location/src" ] && echo "存在" || echo "不存在")"
            fi
        fi
    done

    if [ -n "$source_dir" ]; then
        log_info "找到项目目录: $source_dir"
        log_info "部署项目到: $APP_DIR"

        # 复制项目文件
        if [ "$source_dir" != "$APP_DIR" ]; then
            sudo cp -r "$source_dir"/* "$APP_DIR/"
        else
            log_info "项目已在目标位置，跳过复制"
        fi

        sudo chown -R "$APP_USER:$APP_USER" "$APP_DIR"

        # 设置脚本执行权限
        sudo chmod +x "$APP_DIR/scripts/"*.sh 2>/dev/null || true
        sudo chmod +x "$APP_DIR/main.py" 2>/dev/null || true

        log_success "应用部署完成: $APP_DIR"
    else
        log_error "未找到正确的项目结构"
        echo ""
        echo "期望的项目结构 (main.py + src目录):"
        echo "  项目目录/"
        echo "  ├── main.py"
        echo "  ├── src/"
        echo "  ├── requirements.txt"
        echo "  └── scripts/"
        echo ""
        echo "检查的位置:"
        for location in "${possible_locations[@]}"; do
            echo "  $location: $([ -d "$location" ] && echo "目录存在" || echo "目录不存在")"
            if [ -d "$location" ]; then
                echo "    main.py: $([ -f "$location/main.py" ] && echo "存在" || echo "不存在")"
                echo "    src目录: $([ -d "$location/src" ] && echo "存在" || echo "不存在")"
            fi
        done
        echo ""
        log_error "请确保项目结构正确后重新运行安装脚本"
        exit 1
    fi
}

# 设置Python环境
setup_python_env() {
    log_info "设置Python虚拟环境..."
    
    # 切换到应用用户执行
    sudo -u "$APP_USER" bash << EOF
cd "$APP_DIR"
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
EOF
    
    log_success "Python环境设置完成"
}

# 配置Supervisor
configure_supervisor() {
    log_info "配置Supervisor..."
    
    sudo tee /etc/supervisor/conf.d/starbucks_bypass.conf > /dev/null << EOF
[program:starbucks_bypass]
command=$APP_DIR/venv/bin/python main.py server --port 8000 --host 0.0.0.0
directory=$APP_DIR
user=$APP_USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/starbucks_bypass.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PYTHONPATH="$APP_DIR/src"
EOF
    
    # 重新加载配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    log_success "Supervisor配置完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    sudo tee /etc/nginx/sites-available/starbucks_bypass > /dev/null << EOF
server {
    listen 8094;
    server_name localhost;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        access_log off;
    }
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/starbucks_bypass /etc/nginx/sites-enabled/
    
    # 测试配置
    sudo nginx -t
    
    log_success "Nginx配置完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."

    # 检测当前SSH端口
    local ssh_port=$(grep -E "^Port " /etc/ssh/sshd_config 2>/dev/null | awk '{print $2}' || echo "22")

    log_info "检测到SSH端口: $ssh_port"

    # 启用UFW前先允许SSH端口，避免锁定
    log_warning "配置防火墙前先允许SSH端口，避免连接中断..."

    # 强制允许用户指定的SSH端口28262
    log_info "强制允许用户SSH端口: 28262"
    sudo ufw allow 28262

    # 允许标准SSH端口
    sudo ufw allow 22

    # 如果检测到其他自定义SSH端口，也允许它
    if [ "$ssh_port" != "22" ] && [ "$ssh_port" != "28262" ]; then
        log_info "允许检测到的SSH端口: $ssh_port"
        sudo ufw allow $ssh_port
    fi

    # 启用UFW
    sudo ufw --force enable

    # 允许应用端口
    sudo ufw allow 8000

    # 允许Nginx端口
    sudo ufw allow 8094

    log_success "防火墙配置完成"

    # 显示防火墙状态
    echo ""
    log_info "当前防火墙规则:"
    sudo ufw status

    # 特别验证SSH端口28262
    echo ""
    log_info "验证SSH端口28262:"
    if sudo ufw status | grep -q "28262"; then
        log_success "[成功] SSH端口28262已开放"
    else
        log_error "[错误] SSH端口28262未开放，正在修复..."
        sudo ufw allow 28262
        sudo ufw reload
        log_success "[成功] SSH端口28262已强制开放"
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动Supervisor
    sudo systemctl enable supervisor
    sudo systemctl start supervisor
    
    # 启动Nginx
    sudo systemctl enable nginx
    sudo systemctl start nginx
    
    # 启动应用
    sudo supervisorctl start starbucks_bypass
    
    log_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    if sudo supervisorctl status starbucks_bypass | grep -q "RUNNING"; then
        log_success "应用服务运行正常"
    else
        log_error "应用服务启动失败"
    fi
    
    if systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务启动失败"
    fi
    
    # 检查端口
    if netstat -tlnp | grep -q ":8000"; then
        log_success "应用端口8000监听正常"
    else
        log_warning "应用端口8000未监听"
    fi
    
    if netstat -tlnp | grep -q ":8094"; then
        log_success "Nginx端口8094监听正常"
    else
        log_warning "Nginx端口8094未监听"
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo
    echo "=========================================="
    echo "           部署完成信息"
    echo "=========================================="
    echo
    echo "[工具] 系统信息:"
    echo "  应用目录: $APP_DIR"
    echo "  应用用户: $APP_USER"
    echo "  用户密码: $APP_PASSWORD"
    echo "  应用端口: 8000"
    echo "  Web端口: 8094"
    echo
    echo "🔑 登录信息:"
    echo "  SSH登录: ssh $APP_USER@your-server-ip"
    echo "  用户密码: $APP_PASSWORD"
    echo "  sudo权限: 已配置"
    echo
    echo "📋 管理命令:"
    echo "  查看状态: sudo supervisorctl status starbucks_bypass"
    echo "  重启应用: sudo supervisorctl restart starbucks_bypass"
    echo "  查看日志: sudo tail -f /var/log/starbucks_bypass.log"
    echo "  停止应用: sudo supervisorctl stop starbucks_bypass"
    echo
    echo "🧪 测试命令:"
    echo "  健康检查: curl http://localhost:8094/health"
    echo "  设备状态: curl http://localhost:8094/devices"
    echo "  API文档: http://your-server-ip:8094/docs"
    echo
    echo "🗑️ 卸载命令:"
    echo "  完全卸载: ./scripts/uninstall_ubuntu.sh"
    echo "  (将删除用户 $APP_USER 和所有数据)"
    echo
    echo "[警告]  重要提醒:"
    echo "  - 请妥善保管用户密码: $APP_PASSWORD"
    echo "  - 建议修改默认密码: passwd"
    echo "  - 定期备份重要数据"
    echo
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "  星巴克设备指纹绕过系统 - 快速安装"
    echo "=========================================="
    echo
    
    # 检查权限 - 禁止root用户直接执行
    if [[ $EUID -eq 0 ]]; then
        log_error "安全限制: 禁止使用root用户直接执行此脚本"
        echo ""
        echo "原因:"
        echo "  - 避免系统安全风险"
        echo "  - 防止意外的系统修改"
        echo "  - 确保应用运行在专用用户下"
        echo ""
        echo "正确的执行方式:"
        echo "  1. 使用普通用户登录系统"
        echo "  2. 确保该用户具有sudo权限"
        echo "  3. 运行: ./scripts/install_ubuntu.sh"
        echo ""
        echo "脚本将自动:"
        echo "  - 创建专用用户: $APP_USER"
        echo "  - 配置用户权限和环境"
        echo "  - 在专用用户下运行应用"
        echo ""
        exit 1
    fi

    echo ""
    log_info "安全模式安装:"
    echo "  - 当前用户: $(whoami)"
    echo "  - 需要用户: $APP_USER (请先运行 create_user.sh 创建)"
    echo "  - 应用将安装到: $APP_DIR"
    echo "  - 应用将在用户 $APP_USER 下运行"
    echo ""

    log_info "前置要求:"
    echo "  1. 已使用 root 运行 ./scripts/create_user.sh"
    echo "  2. 已切换到 $APP_USER 用户"
    echo "  3. 当前在项目目录中"
    echo ""

    # 确认安装
    log_info "此脚本将在Ubuntu服务器上安装星巴克设备指纹绕过系统"
    echo ""
    echo "安装内容:"
    echo "  - 系统依赖包 (Python, Nginx, Supervisor等) - 不更新系统"
    echo "  - 检查应用用户 (必须已存在)"
    echo "  - 应用代码和配置"
    echo "  - 系统服务配置"
    echo ""
    read -p "确认继续安装? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消安装操作"
        exit 0
    fi
    
    echo
    log_info "开始安装过程..."
    echo
    
    # 执行安装步骤
    check_system
    echo

    install_dependencies
    echo

    check_user_exists
    echo

    deploy_application
    echo
    
    setup_python_env
    echo
    
    configure_supervisor
    echo
    
    configure_nginx
    echo
    
    configure_firewall
    echo
    
    start_services
    echo
    
    verify_deployment
    echo
    
    show_deployment_info
    
    log_success "安装完成！"
}

# 执行主函数
main "$@"
