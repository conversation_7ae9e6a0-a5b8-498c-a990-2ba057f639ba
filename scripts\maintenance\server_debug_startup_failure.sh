#!/bin/bash

# 调试服务启动失败问题
echo "🔍 调试服务启动失败问题..."

# 1. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 2. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 3. 检查supervisor配置
echo "📋 检查supervisor配置..."
echo "=== supervisor配置文件 ==="
cat /etc/supervisor/conf.d/starbucks_bypass.conf

# 4. 检查启动日志
echo ""
echo "📋 检查启动日志..."
echo "=== 最近的输出日志 ==="
tail -20 logs/output.log 2>/dev/null || echo "无输出日志文件"

echo ""
echo "=== 最近的错误日志 ==="
tail -20 logs/error.log 2>/dev/null || echo "无错误日志文件"

echo ""
echo "=== supervisor日志 ==="
sudo tail -20 /var/log/supervisor/supervisord.log 2>/dev/null || echo "无supervisor日志"

# 5. 手动测试启动命令
echo ""
echo "🧪 手动测试启动命令..."
echo "测试启动命令:"

# 从supervisor配置中提取启动命令
start_command=$(grep "^command=" /etc/supervisor/conf.d/starbucks_bypass.conf | cut -d'=' -f2-)
echo "启动命令: $start_command"

# 6. 测试Python路径和模块导入
echo ""
echo "🔍 测试Python路径和模块导入..."
cd /home/<USER>/apps/starbucks_bypass_tester/src

echo "当前工作目录: $(pwd)"
echo "Python路径:"
python3 -c "import sys; print('\n'.join(sys.path))"

echo ""
echo "测试API服务模块导入:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.api_service import create_app
    print('✅ create_app 导入成功')
    
    # 测试创建app
    app = create_app()
    print('✅ app 创建成功')
    print(f'app类型: {type(app)}')
except Exception as e:
    print(f'❌ API服务测试失败: {e}')
    import traceback
    traceback.print_exc()
"

# 7. 测试uvicorn启动
echo ""
echo "🚀 测试uvicorn启动..."
echo "测试命令: python3 -m uvicorn core.api_service:app --host 0.0.0.0 --port 8000"

# 设置超时测试
timeout 10s python3 -m uvicorn core.api_service:app --host 0.0.0.0 --port 8000 &
uvicorn_pid=$!

sleep 5

# 检查进程是否还在运行
if kill -0 $uvicorn_pid 2>/dev/null; then
    echo "✅ uvicorn启动成功，进程ID: $uvicorn_pid"
    kill $uvicorn_pid
    wait $uvicorn_pid 2>/dev/null
else
    echo "❌ uvicorn启动失败"
fi

# 8. 检查端口占用
echo ""
echo "🔍 检查端口占用..."
echo "检查8000端口:"
netstat -tlnp | grep :8000 || echo "端口8000未被占用"

echo ""
echo "检查所有Python进程:"
ps aux | grep python | grep -v grep || echo "无Python进程运行"

# 9. 检查文件权限
echo ""
echo "🔍 检查文件权限..."
echo "项目目录权限:"
ls -la /home/<USER>/apps/starbucks_bypass_tester/

echo ""
echo "src目录权限:"
ls -la /home/<USER>/apps/starbucks_bypass_tester/src/

echo ""
echo "core目录权限:"
ls -la /home/<USER>/apps/starbucks_bypass_tester/src/core/

echo ""
echo "api_service.py权限:"
ls -la /home/<USER>/apps/starbucks_bypass_tester/src/core/api_service.py

# 10. 检查配置文件
echo ""
echo "🔍 检查配置文件..."
echo "config目录:"
ls -la /home/<USER>/apps/starbucks_bypass_tester/src/config/ 2>/dev/null || echo "config目录不存在"

echo ""
echo "配置文件:"
ls -la /home/<USER>/apps/starbucks_bypass_tester/src/config/*.yaml 2>/dev/null || echo "无yaml配置文件"
ls -la /home/<USER>/apps/starbucks_bypass_tester/src/config/*.json 2>/dev/null || echo "无json配置文件"

# 11. 测试直接运行API服务
echo ""
echo "🧪 测试直接运行API服务..."
cd /home/<USER>/apps/starbucks_bypass_tester/src

echo "直接运行测试:"
python3 -c "
import sys
import os
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    print('开始导入模块...')
    from core.api_service import create_app
    print('✅ 模块导入成功')
    
    print('开始创建应用...')
    app = create_app()
    print('✅ 应用创建成功')
    
    print('开始导入uvicorn...')
    import uvicorn
    print('✅ uvicorn导入成功')
    
    print('准备启动服务...')
    print('如果这里卡住，说明服务启动过程中有问题')
    
except Exception as e:
    print(f'❌ 直接运行失败: {e}')
    import traceback
    traceback.print_exc()
"

# 12. 生成详细的错误报告
echo ""
echo "📋 生成错误报告..."
echo "================================"
echo "错误报告摘要:"
echo "1. 依赖检查: ✅ 所有依赖包已安装"
echo "2. 模块导入: ✅ 所有模块导入成功"
echo "3. 服务启动: ❌ supervisor启动失败"
echo "4. 错误类型: FATAL Exited too quickly"
echo ""
echo "可能的原因:"
echo "- supervisor配置问题"
echo "- 启动命令路径问题"
echo "- 工作目录问题"
echo "- 权限问题"
echo "- 端口冲突"
echo "- 配置文件缺失"
echo ""
echo "建议检查:"
echo "1. 查看详细的错误日志"
echo "2. 手动测试启动命令"
echo "3. 检查supervisor配置"
echo "4. 验证文件权限"
echo "================================"

deactivate
