#!/bin/bash

# 修复绕过引擎初始化问题
echo "🔧 修复绕过引擎初始化问题..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 检查依赖
echo "🔍 检查关键依赖..."
pip list | grep -E "(fastapi|uvicorn|pydantic)" || {
    echo "❌ 关键依赖缺失，重新安装..."
    pip install fastapi uvicorn pydantic
}

# 4. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 5. 清理缓存
echo "🧹 清理Python缓存..."
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 6. 检查API服务初始化代码
echo "🔍 检查API服务初始化..."
python3 << 'CHECK_INIT'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    # 检查API服务文件
    with open('src/core/api_service.py', 'r') as f:
        content = f.read()
    
    # 检查是否有正确的初始化代码
    if 'bypass_engine = None' in content:
        print('✅ 找到bypass_engine变量定义')
    else:
        print('❌ 未找到bypass_engine变量定义')
    
    if '@app.on_event("startup")' in content:
        print('✅ 找到startup事件处理')
    else:
        print('❌ 未找到startup事件处理')
    
    if 'bypass_engine = BypassEngine(' in content:
        print('✅ 找到bypass_engine初始化')
    else:
        print('❌ 未找到bypass_engine初始化')
        
except Exception as e:
    print(f'❌ 检查失败: {e}')
CHECK_INIT

# 7. 修复API服务初始化问题
echo "🔧 修复API服务初始化..."
python3 << 'FIX_INIT'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

# 读取API服务文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

print('🔧 检查并修复初始化问题...')

# 检查是否有全局变量定义
if 'bypass_engine = None' not in content:
    print('  🔧 添加全局变量定义...')
    # 在导入后添加全局变量
    import_section = content.find('from core.bypass_engine import')
    if import_section != -1:
        # 找到导入部分的结尾
        next_line = content.find('\n', import_section)
        if next_line != -1:
            # 在导入后添加全局变量定义
            content = content[:next_line] + '\n\n# 全局变量\nbypass_engine = None\ndevice_fingerprint_engine = None\n' + content[next_line:]
            print('  ✅ 全局变量定义已添加')

# 检查startup事件
if '@app.on_event("startup")' not in content:
    print('  🔧 添加startup事件处理...')
    # 在app定义后添加startup事件
    app_definition = content.find('app = FastAPI(')
    if app_definition != -1:
        # 找到app定义的结尾
        next_section = content.find('\n\n', app_definition)
        if next_section != -1:
            startup_code = '''

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化组件"""
    global bypass_engine, device_fingerprint_engine
    
    try:
        print("🚀 初始化设备指纹引擎...")
        device_fingerprint_engine = DeviceFingerprintEngine()
        await device_fingerprint_engine.initialize()
        
        print("🚀 初始化绕过引擎...")
        bypass_engine = BypassEngine(
            fingerprint_engine=device_fingerprint_engine
        )
        await bypass_engine.initialize()
        
        print("✅ 所有组件初始化完成")
        
    except Exception as e:
        print(f"❌ 组件初始化失败: {e}")
        raise e
'''
            content = content[:next_section] + startup_code + content[next_section:]
            print('  ✅ startup事件处理已添加')

# 检查单次绕过函数中的引擎检查
if 'if bypass_engine is None:' not in content:
    print('  🔧 修复单次绕过函数...')
    # 找到单次绕过函数
    single_bypass_func = content.find('async def bypass_single(')
    if single_bypass_func != -1:
        # 找到函数体开始
        func_body_start = content.find(':', single_bypass_func)
        func_body_start = content.find('\n', func_body_start) + 1
        
        # 添加引擎检查
        engine_check = '''    global bypass_engine
    
    if bypass_engine is None:
        raise HTTPException(
            status_code=500,
            detail="绕过引擎未初始化"
        )
    
'''
        content = content[:func_body_start] + engine_check + content[func_body_start:]
        print('  ✅ 单次绕过函数引擎检查已添加')

# 写入修复后的文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ API服务初始化修复完成')
FIX_INIT

# 8. 验证语法
echo "🔍 验证语法..."
cd src
python3 -m py_compile core/api_service.py
if [ $? -eq 0 ]; then
    echo "✅ 语法验证通过"
else
    echo "❌ 语法验证失败"
    exit 1
fi

# 9. 测试模块导入
echo "🔍 测试模块导入..."
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    from core.api_service import app
    print('✅ API服务模块导入成功')
except Exception as e:
    print(f'❌ API服务模块导入失败: {e}')
    exit(1)
"

# 10. 重启服务
echo "🚀 重启服务..."
cd /home/<USER>/apps/starbucks_bypass_tester
sudo supervisorctl start starbucks_bypass

# 11. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 25

# 12. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 13. 检查启动日志
echo "📋 检查启动日志..."
echo "最近的输出日志:"
tail -15 logs/output.log 2>/dev/null || echo "无输出日志"

# 14. 测试健康检查
echo "🧪 测试健康检查..."
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败: $health_response"
fi

# 15. 测试单次绕过API
echo "🧪 测试单次绕过API..."
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
    echo ""
    echo "🎉 绕过引擎初始化修复成功！现在运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
    echo ""
    echo "⚠️ 需要查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
