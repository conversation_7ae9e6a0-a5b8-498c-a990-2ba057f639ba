#!/bin/bash

# 完整的服务器修复指令
# 解决sed重复替换导致的语法错误

echo "🚨 检测到服务器代码损坏，开始完整修复..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 恢复备份文件（如果存在）
echo "📦 恢复备份文件..."
if [ -f "src/core/api_service.py.backup" ]; then
    cp src/core/api_service.py.backup src/core/api_service.py
    echo "✅ 恢复 api_service.py"
fi

if [ -f "src/core/bypass_engine.py.backup" ]; then
    cp src/core/bypass_engine.py.backup src/core/bypass_engine.py
    echo "✅ 恢复 bypass_engine.py"
fi

if [ -f "src/core/device_fingerprint_engine.py.backup" ]; then
    cp src/core/device_fingerprint_engine.py.backup src/core/device_fingerprint_engine.py
    echo "✅ 恢复 device_fingerprint_engine.py"
fi

# 3. 创建安全修复脚本
echo "🔧 创建安全修复脚本..."
cat > safe_repair.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def fix_bypass_engine():
    """修复bypass_engine.py的类型注解"""
    file_path = 'src/core/bypass_engine.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: DeviceFingerprintEngine类型注解
    if "fingerprint_engine: DeviceFingerprintEngine = None" in content:
        content = content.replace(
            "fingerprint_engine: DeviceFingerprintEngine = None",
            "fingerprint_engine: 'DeviceFingerprintEngine' = None"
        )
        print("✅ 修复 DeviceFingerprintEngine 类型注解")
    
    # 修复2: DeviceFingerprint类型注解
    if "fingerprint: DeviceFingerprint) -> float:" in content:
        content = content.replace(
            "fingerprint: DeviceFingerprint) -> float:",
            "fingerprint: 'DeviceFingerprint') -> float:"
        )
        print("✅ 修复 DeviceFingerprint 类型注解")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def fix_api_service():
    """修复api_service.py的方法调用和返回值访问"""
    file_path = 'src/core/api_service.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: 方法名
    if "self.bypass_engine.get_bypass_stats()" in content:
        content = content.replace(
            "self.bypass_engine.get_bypass_stats()",
            "await self.bypass_engine.get_bypass_statistics()"
        )
        print("✅ 修复 API 方法调用")
    
    # 修复2: 参数传递
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if "request.data," in line and "data=" not in line:
            lines[i] = line.replace("request.data,", "data=request.data,")
            print("✅ 修复 request.data 参数")
        if "bypass_req.data," in line and "data=" not in line:
            lines[i] = line.replace("bypass_req.data,", "data=bypass_req.data,")
            print("✅ 修复 bypass_req.data 参数")
        if line.strip() == "strategy" and i > 0 and "strategy=" not in lines[i-1]:
            lines[i] = line.replace("strategy", "strategy=strategy")
            print("✅ 修复 strategy 参数")
    
    content = '\n'.join(lines)
    
    # 修复3: 返回值访问
    replacements = [
        ("result.success", "result['success']"),
        ("result.risk_level.value", "result['risk_level']"),
        ("result.confidence", "result['confidence']"),
        ("result.fingerprint_quality", "result['fingerprint_quality']"),
        ("result.bypass_techniques", "result['bypass_techniques']"),
        ("result.warnings", "result['warnings']"),
        ("result.execution_time", "result['execution_time']"),
        ("result.timestamp.isoformat()", "result['timestamp']"),
    ]
    
    for old, new in replacements:
        if old in content and new not in content:
            content = content.replace(old, new)
            print(f"✅ 修复 {old} -> {new}")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def fix_device_fingerprint():
    """修复device_fingerprint_engine.py添加缺失方法"""
    file_path = 'src/core/device_fingerprint_engine.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加success_rate字段
    if 'success_rate: float = 1.0' not in content:
        target = 'additional_headers: Optional[Dict[str, str]] = None  # 添加additional_headers参数以兼容测试'
        if target in content:
            content = content.replace(
                target,
                target + '\n    success_rate: float = 1.0  # 添加成功率字段'
            )
            print("✅ 添加 success_rate 字段")
    
    # 添加方法
    if 'def is_available(self) -> bool:' not in content:
        methods_code = '''
    
    def is_available(self) -> bool:
        """检查设备指纹是否可用"""
        if not self.is_active:
            return False
        
        # 检查冷却时间
        if self.last_used:
            from datetime import timedelta
            cooldown_minutes = 5  # 默认冷却时间5分钟
            cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
            if datetime.now() < cooldown_end:
                return False
        
        return True
    
    def get_cooldown_remaining(self) -> float:
        """获取剩余冷却时间（分钟）"""
        if not self.last_used:
            return 0.0
        
        from datetime import timedelta
        cooldown_minutes = 5  # 默认冷却时间5分钟
        cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
        now = datetime.now()
        
        if now >= cooldown_end:
            return 0.0
        
        remaining = cooldown_end - now
        return remaining.total_seconds() / 60.0  # 转换为分钟'''
        
        # 在success_rate字段后添加方法
        target = 'success_rate: float = 1.0  # 添加成功率字段'
        if target in content:
            content = content.replace(target, target + methods_code)
            print("✅ 添加 is_available 和 get_cooldown_remaining 方法")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def main():
    print("🔧 开始安全修复...")
    
    try:
        fix_bypass_engine()
        fix_api_service()
        fix_device_fingerprint()
        print("🎉 所有修复完成！")
        return True
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
EOF

# 4. 运行安全修复
echo "🔧 运行安全修复..."
python3 safe_repair.py

# 5. 验证语法
echo "🔍 验证语法..."
python3 -m py_compile src/core/bypass_engine.py
if [ $? -ne 0 ]; then
    echo "❌ bypass_engine.py 语法错误"
    exit 1
fi

python3 -m py_compile src/core/api_service.py
if [ $? -ne 0 ]; then
    echo "❌ api_service.py 语法错误"
    exit 1
fi

python3 -m py_compile src/core/device_fingerprint_engine.py
if [ $? -ne 0 ]; then
    echo "❌ device_fingerprint_engine.py 语法错误"
    exit 1
fi

echo "✅ 所有文件语法检查通过"

# 6. 清理临时文件
rm -f safe_repair.py

# 7. 重启服务
echo "🚀 重启服务..."
sudo supervisorctl start starbucks_bypass

# 8. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 9. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 10. 测试API
echo "🧪 测试API..."
echo "测试健康检查:"
curl -s http://localhost:8000/health

echo -e "\n测试统计信息:"
curl -s http://localhost:8000/stats | jq '.'

echo -e "\n测试设备列表:"
curl -s http://localhost:8000/devices | jq '.'

echo -e "\n测试单次绕过:"
curl -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' | jq '.'

echo -e "\n🎉 修复完成！所有API应该正常工作"
