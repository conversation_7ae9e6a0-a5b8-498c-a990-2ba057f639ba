#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
# [符号][符号] "BACKOFF Exited too quickly" [符号][符号]

echo "=== [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] ==="

APP_DIR="/home/<USER>/apps/starbucks_bypass_tester"

# 1. [符号][符号][符号][符号][符号][符号][符号][符号]
echo "1. [符号][符号][符号][符号][符号][符号][符号][符号]..."
sudo supervisorctl status starbucks_bypass

# 2. [符号][符号][符号][符号][符号][符号][符号][符号]
echo "2. [符号][符号][符号][符号][符号][符号]..."
echo "--- Supervisor[符号][符号] ---"
sudo supervisorctl tail starbucks_bypass
echo ""
echo "--- [符号][符号][符号][符号] ---"
if [ -f "/var/log/starbucks_bypass.log" ]; then
    tail -20 /var/log/starbucks_bypass.log
else
    echo "[符号][符号][符号][符号][符号][符号][符号][符号][符号]"
fi

# 3. [符号][符号][符号][符号][符号][符号][符号][符号]
echo "3. [符号][符号][符号][符号][符号][符号][符号][符号]..."
echo "[符号][符号][符号][符号]: $APP_DIR"
echo "main.py: $([ -f "$APP_DIR/main.py" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
echo "venv: $([ -d "$APP_DIR/venv" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
echo "src[符号][符号]: $([ -d "$APP_DIR/src" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
echo "requirements.txt: $([ -f "$APP_DIR/requirements.txt" ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"

# 4. [符号][符号]Python[符号][符号]
echo "4. [符号][符号]Python[符号][符号]..."
if [ -f "$APP_DIR/venv/bin/python" ]; then
    echo "Python[符号][符号]:"
    $APP_DIR/venv/bin/python --version
    
    echo "[符号][符号]main.py[符号][符号]:"
    cd "$APP_DIR"
    if $APP_DIR/venv/bin/python -m py_compile main.py; then
        echo "   [[符号][符号]] main.py[符号][符号][符号][符号]"
    else
        echo "   [[符号][符号]] main.py[符号][符号][符号][符号]"
    fi
    
    echo "[符号][符号][符号][符号][符号][符号]:"
    if $APP_DIR/venv/bin/python -c "import sys; sys.path.insert(0, 'src'); print('PYTHONPATH[符号][符号][符号][符号]')" 2>/dev/null; then
        echo "   [[符号][符号]] PYTHONPATH[符号][符号][符号][符号]"
    else
        echo "   [[符号][符号]] PYTHONPATH[符号][符号][符号][符号]"
    fi
    
    echo "[符号][符号][符号][符号][符号][符号][符号][符号]:"
    if $APP_DIR/venv/bin/python -c "import sys; sys.path.insert(0, 'src'); from core.api_service import APIService; print('APIService[符号][符号][符号][符号]')" 2>/dev/null; then
        echo "   [[符号][符号]] APIService[符号][符号][符号][符号][符号][符号]"
    else
        echo "   [[符号][符号]] APIService[符号][符号][符号][符号][符号][符号]"
        echo "   [符号][符号][符号][符号][符号][符号][符号][符号]:"
        $APP_DIR/venv/bin/python -c "import sys; sys.path.insert(0, 'src'); from core.api_service import APIService" 2>&1 || true
    fi
else
    echo "   [[符号][符号]] Python[符号][符号][符号][符号][符号][符号][符号]"
fi

# 5. [符号][符号][符号][符号][符号][符号][符号][符号]
echo "5. [符号][符号][符号][符号][符号][符号][符号][符号]..."
cd "$APP_DIR"
if [ -f "venv/bin/python" ] && [ -f "main.py" ]; then
    echo "[符号][符号][符号][符号][符号][符号][符号][符号] (10[符号][符号][符号])..."
    
    # [符号][符号][符号][符号][符号][符号]
    export PYTHONPATH="$APP_DIR/src"
    
    # [符号][符号][符号][符号][符号][符号]
    timeout 10s $APP_DIR/venv/bin/python main.py server --port 8000 --host 0.0.0.0 2>&1 &
    START_PID=$!
    
    sleep 3
    
    # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    if kill -0 $START_PID 2>/dev/null; then
        echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]"
        # [符号][符号]API
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            echo "   [[符号][符号]] API[符号][符号][符号][符号]"
        else
            echo "   [[符号][符号]] API[符号][符号][符号]"
        fi
        # [符号][符号][符号][符号][符号][符号]
        kill $START_PID 2>/dev/null || true
    else
        echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]"
        wait $START_PID
        echo "   [符号][符号][符号][符号]: $?"
    fi
else
    echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号]"
fi

# 6. [符号][符号][符号][符号][符号]
echo "6. [符号][符号][符号][符号][符号]..."
cd "$APP_DIR"
source venv/bin/activate 2>/dev/null || true
echo "[符号][符号][符号][符号][符号][符号][符号]:"
pip list 2>/dev/null | grep -E "(fastapi|uvicorn|pydantic)" || echo "   [[符号][符号]] [符号][符号][符号][符号][符号]"

# 7. [符号][符号][符号][符号][符号][符号]
echo "7. [符号][符号][符号][符号][符号][符号]..."

# 7.1 [符号][符号][符号][符号][符号][符号]
echo "7.1 [符号][符号][符号][符号][符号][符号]..."
cd "$APP_DIR"
source venv/bin/activate
pip install --upgrade pip > /dev/null 2>&1
pip install -r requirements.txt > /dev/null 2>&1
echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]"

# 7.2 [符号][符号][符号][符号]
echo "7.2 [符号][符号][符号][符号][符号][符号]..."
chown -R starbucks:starbucks "$APP_DIR"
chmod +x "$APP_DIR/venv/bin/python"
echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号]"

# 7.3 [符号][符号][符号][符号][符号][符号]
echo "7.3 [符号][符号][符号][符号][符号][符号]..."
mkdir -p "$APP_DIR/logs"
chown starbucks:starbucks "$APP_DIR/logs"
echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]"

# 8. [符号][符号][符号][符号]Supervisor
echo "8. [符号][符号][符号][符号]Supervisor..."
sudo tee /etc/supervisor/conf.d/starbucks_bypass.conf > /dev/null << EOF
[program:starbucks_bypass]
command=$APP_DIR/venv/bin/python main.py server --port 8000 --host 0.0.0.0
directory=$APP_DIR
user=starbucks
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/starbucks_bypass.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile=/var/log/starbucks_bypass_error.log
environment=PYTHONPATH="$APP_DIR/src"
startsecs=10
startretries=3
EOF
echo "   [[符号][符号]] Supervisor[符号][符号][符号][符号][符号][符号]"

# 9. [符号][符号][符号][符号][符号][符号]
echo "9. [符号][符号][符号][符号][符号][符号]..."
sudo supervisorctl stop starbucks_bypass 2>/dev/null || true
sudo pkill -f "python.*main.py" 2>/dev/null || true
sudo fuser -k 8000/tcp 2>/dev/null || true
sleep 2
echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]"

# 10. [符号][符号][符号][符号][符号][符号]
echo "10. [符号][符号][符号][符号][符号][符号]..."
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start starbucks_bypass

# 11. [符号][符号][符号][符号][符号][符号][符号]
echo "11. [符号][符号][符号][符号][符号][符号]..."
sleep 5

# [符号][符号][符号][符号][符号][符号]
SERVICE_STATUS=$(sudo supervisorctl status starbucks_bypass)
echo "[符号][符号][符号][符号]: $SERVICE_STATUS"

if echo "$SERVICE_STATUS" | grep -q "RUNNING"; then
    echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号]"
    
    # [符号][符号]API
    if curl -s http://localhost:8000/health > /dev/null; then
        echo "   [[符号][符号]] API[符号][符号][符号][符号]"
        echo "   API[符号][符号][符号][符号]:"
        curl -s http://localhost:8000/health | jq . 2>/dev/null || curl -s http://localhost:8000/health
    else
        echo "   [[符号][符号]] API[符号][符号][符号]"
    fi
else
    echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号]"
    echo "   [符号][符号][符号][符号][符号][符号]:"
    sudo supervisorctl tail starbucks_bypass
fi

echo ""
echo "=== [符号][符号][符号][符号][符号][符号][符号] ==="

# 12. [符号][符号][符号][符号][符号][符号][符号]
echo "12. [符号][符号][符号][符号][符号]..."
if echo "$SERVICE_STATUS" | grep -q "RUNNING"; then
    echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]"
    echo "   [符号][符号][符号][符号]: http://your-server-ip:8094"
    echo "   API[符号][符号]: http://your-server-ip:8094/docs"
else
    echo "   [[符号][符号]]  [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]:"
    echo "   1. [符号][符号][符号][符号][符号][符号]: sudo supervisorctl tail starbucks_bypass"
    echo "   2. [符号][符号][符号][符号][符号][符号]: tail -50 /var/log/starbucks_bypass_error.log"
    echo "   3. [符号][符号][符号][符号][符号][符号]: cd $APP_DIR && venv/bin/python main.py server --port 8000"
fi
