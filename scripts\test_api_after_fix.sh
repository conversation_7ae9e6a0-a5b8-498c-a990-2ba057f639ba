#!/bin/bash

# API修复后测试脚本
echo "🧪 API修复后完整测试..."

# 设置测试目标
API_BASE="http://localhost:8000"
TIMEOUT=30

echo "测试目标: $API_BASE"
echo "超时时间: ${TIMEOUT}秒"
echo ""

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "--------------------------------------------------------------------------------"
    echo "[测试] $name"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s --max-time $TIMEOUT "$API_BASE$endpoint" 2>/dev/null)
    else
        response=$(curl -s --max-time $TIMEOUT -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE$endpoint" 2>/dev/null)
    fi
    
    if [ $? -eq 0 ] && [ -n "$response" ]; then
        # 检查是否包含错误信息
        if echo "$response" | grep -q '"detail":\|"error":\|"message":'; then
            echo "[错误] $name 测试失败 (API错误)"
            echo "响应: $response" | head -3
            echo "[错误] $name: 失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        else
            echo "[成功] $name 测试成功"
            echo "[成功] $name: 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        fi
    else
        echo "[错误] $name 测试失败 (连接错误)"
        echo "[错误] $name: 失败"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 执行测试
echo "================================================================================"
echo "API测试套件 - 修复验证版本"
echo "================================================================================"

# 1. 健康检查
test_api "健康检查" "GET" "/health" ""

# 2. 服务信息
test_api "服务信息" "GET" "/info" ""

# 3. 统计信息
test_api "统计信息" "GET" "/stats" ""

# 4. 设备列表
test_api "设备列表" "GET" "/devices" ""

# 5. 单次绕过 (关键测试)
test_api "单次绕过" "POST" "/bypass/single" '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
}'

# 6. 批量绕过
test_api "批量绕过" "POST" "/bypass/batch" '{
    "requests": [
        {
            "target_url": "https://httpbin.org/get",
            "method": "GET",
            "strategy": "adaptive"
        }
    ],
    "max_concurrent": 1
}'

# 输出测试结果
echo ""
echo "================================================================================"
echo "测试结果总结"
echo "================================================================================"
echo "总测试数: $TOTAL_TESTS"
echo "通过测试: $PASSED_TESTS"
echo "失败测试: $FAILED_TESTS"

if [ $TOTAL_TESTS -gt 0 ]; then
    PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo "通过率: ${PASS_RATE}%"
else
    echo "通过率: 0%"
fi

echo "================================================================================"

# 判断测试结果
if [ $FAILED_TESTS -eq 0 ]; then
    echo "🎉 所有测试通过！API修复成功！"
    exit 0
else
    echo "⚠️ 部分测试失败，请检查API服务状态。"
    if [ $PASSED_TESTS -eq 5 ] && [ $FAILED_TESTS -eq 1 ]; then
        echo "💡 如果只有单次绕过失败，说明修复可能未完全应用。"
    fi
    exit 1
fi
