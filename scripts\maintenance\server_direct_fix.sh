#!/bin/bash

# 直接修复服务器API文件 - 避免所有转义问题
echo "🔧 直接修复API服务文件..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 备份当前文件
echo "📦 备份当前文件..."
cp src/core/api_service.py src/core/api_service.py.broken.$(date +%Y%m%d_%H%M%S)

# 3. 从最早的干净备份恢复
echo "🔄 从干净备份恢复..."
CLEAN_BACKUP=$(ls -tr src/core/api_service.py.backup.* 2>/dev/null | head -1)
if [ -n "$CLEAN_BACKUP" ]; then
    echo "使用备份: $CLEAN_BACKUP"
    cp "$CLEAN_BACKUP" src/core/api_service.py
else
    echo "❌ 未找到备份文件"
    exit 1
fi

# 4. 使用sed进行精确修复（避免Python字符串问题）
echo "🔧 使用sed进行精确修复..."

# 4.1 修复BypassResponse模型 - 添加新字段
echo "  🔧 修复BypassResponse模型..."
if ! grep -q "strategy_used: Optional\[str\] = None" src/core/api_service.py; then
    # 在timestamp行后添加新字段
    sed -i '/timestamp: str$/a\    strategy_used: Optional[str] = None\n    modified_fingerprint: Optional[Dict[str, Any]] = None' src/core/api_service.py
    echo "  ✅ BypassResponse模型字段已添加"
fi

# 4.2 修复单次绕过响应构建
echo "  🔧 修复单次绕过响应构建..."
# 查找包含timestamp=result['timestamp']的行，在其后添加新字段
sed -i '/timestamp=result\[.timestamp.\]$/a\                    strategy_used=result.get("strategy_used"),\n                    modified_fingerprint=None  # 不返回敏感的指纹信息' src/core/api_service.py

# 4.3 修复批量绕过响应构建
echo "  🔧 修复批量绕过响应构建..."
# 查找批量绕过中的timestamp行
sed -i '/timestamp=result\[.timestamp.\]$/,/^[[:space:]]*\)/{
    /timestamp=result\[.timestamp.\]$/a\                                strategy_used=result.get("strategy_used"),\n                                modified_fingerprint=None
}' src/core/api_service.py

# 4.4 修复错误响应构建
echo "  🔧 修复错误响应构建..."
sed -i '/timestamp=datetime\.now()\.isoformat()$/a\                                strategy_used=None,\n                                modified_fingerprint=None' src/core/api_service.py

echo "✅ sed修复完成"

# 5. 手动清理可能的重复行
echo "🧹 清理重复行..."
python3 << 'CLEANUP'
# 读取文件
with open('src/core/api_service.py', 'r') as f:
    lines = f.readlines()

# 去重处理
cleaned_lines = []
seen_strategy_line = False
seen_modified_line = False

for line in lines:
    # 避免重复的strategy_used行
    if 'strategy_used=' in line and 'result.get(' in line:
        if not seen_strategy_line:
            cleaned_lines.append(line)
            seen_strategy_line = True
    # 避免重复的modified_fingerprint行  
    elif 'modified_fingerprint=None' in line and '# 不返回敏感的指纹信息' in line:
        if not seen_modified_line:
            cleaned_lines.append(line)
            seen_modified_line = True
    else:
        cleaned_lines.append(line)

# 写回文件
with open('src/core/api_service.py', 'w') as f:
    f.writelines(cleaned_lines)

print('✅ 重复行清理完成')
CLEANUP

# 6. 验证语法
echo "🔍 验证语法..."
python3 -m py_compile src/core/api_service.py
if [ $? -eq 0 ]; then
    echo "✅ 语法验证通过"
else
    echo "❌ 语法验证失败，显示错误详情："
    python3 -c "
try:
    compile(open('src/core/api_service.py').read(), 'src/core/api_service.py', 'exec')
    print('语法正确')
except SyntaxError as e:
    print(f'语法错误在第 {e.lineno} 行: {e.text.strip() if e.text else \"未知\"}')
    print(f'错误位置: {e.offset}')
    # 显示错误行附近的内容
    with open('src/core/api_service.py') as f:
        lines = f.readlines()
        start = max(0, e.lineno - 3)
        end = min(len(lines), e.lineno + 2)
        for i in range(start, end):
            marker = '>>> ' if i == e.lineno - 1 else '    '
            print(f'{marker}{i+1:3d}: {lines[i].rstrip()}')
"
    echo "恢复到备份..."
    cp "$CLEAN_BACKUP" src/core/api_service.py
    exit 1
fi

# 7. 检查关键字段是否存在
echo "🔍 验证修复内容..."
if grep -q "strategy_used.*result\.get" src/core/api_service.py; then
    echo "✅ 找到strategy_used字段修复"
else
    echo "⚠️ 未找到strategy_used字段修复"
fi

if grep -q "modified_fingerprint.*None" src/core/api_service.py; then
    echo "✅ 找到modified_fingerprint字段修复"
else
    echo "⚠️ 未找到modified_fingerprint字段修复"
fi

# 8. 重启服务
echo "🚀 重启服务..."
sudo supervisorctl start starbucks_bypass

# 9. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 10. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 11. 测试API
echo "🧪 测试单次绕过API..."
response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$response" | head -3
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$response"
fi

echo ""
echo "🎉 修复完成！现在运行完整测试："
echo "./scripts/run_all_tests.sh"
