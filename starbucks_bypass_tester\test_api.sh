#!/bin/bash
# API[符号][符号][符号][符号] - Shell[符号][符号]
# API Test Script - Shell Version

# [符号][符号][符号][符号]
API_URL="http://localhost:8000"
TIMEOUT=30
VERBOSE=false

# [符号][符号][符号][符号]
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# [符号][符号][符号][符号]
show_help() {
    echo "API[符号][符号][符号][符号] - Shell[符号][符号]"
    echo ""
    echo "[符号][符号]: $0 [[符号][符号]]"
    echo ""
    echo "[符号][符号]:"
    echo "  -u, --url URL        API[符号][符号][符号][符号] ([符号][符号]: http://localhost:8000)"
    echo "  -t, --timeout SEC    [符号][符号][符号][符号][符号][符号] ([符号][符号]: 30[符号])"
    echo "  -v, --verbose        [符号][符号][符号][符号]"
    echo "  -h, --help           [符号][符号][符号][符号][符号][符号]"
    echo ""
    echo "[符号][符号]:"
    echo "  $0                                    # [符号][符号][符号][符号][符号][符号]"
    echo "  $0 -u http://server:8000             # [符号][符号][符号][符号][符号][符号]"
    echo "  $0 -u http://localhost:8000 -v       # [符号][符号][符号][符号]"
}

# [符号][符号][符号][符号][符号][符号][符号]
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--url)
            API_URL="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "[符号][符号][符号][符号]: $1"
            show_help
            exit 1
            ;;
    esac
done

# [符号][符号][符号][符号]
log_info() {
    echo -e "${BLUE}[[符号][符号]]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[[符号][符号]]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[[符号][符号]]${NC} $1"
}

log_error() {
    echo -e "${RED}[[符号][符号]]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[[符号][符号]]${NC} $1"
}

# [符号][符号][符号][符号]
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl [符号][符号][符号][符号][符号][符号][符号][符号] curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq [符号][符号][符号][符号]JSON[符号][符号][符号][符号][符号][符号][符号][符号]"
    fi
}

# HTTP[符号][符号][符号][符号]
make_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local url="${API_URL}${endpoint}"
    
    if [ "$VERBOSE" = true ]; then
        log_info "[符号][符号]: $method $url"
        if [ -n "$data" ]; then
            log_info "[符号][符号]: $data"
        fi
    fi
    
    if [ "$method" = "GET" ]; then
        curl -s -w "\n%{http_code}" --max-time "$TIMEOUT" "$url"
    else
        curl -s -w "\n%{http_code}" --max-time "$TIMEOUT" \
             -X "$method" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "$url"
    fi
}

# [符号][符号][符号][符号]
parse_response() {
    local response="$1"
    local status_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    echo "$status_code|$body"
}

# [符号][符号][符号]JSON[符号][符号]
format_json() {
    local json="$1"
    if command -v jq &> /dev/null; then
        echo "$json" | jq .
    else
        echo "$json"
    fi
}

# [符号][符号][符号][符号][符号][符号]
test_service_info() {
    log_test "[符号][符号][符号][符号]"
    
    local response=$(make_request "GET" "/")
    local parsed=$(parse_response "$response")
    local status_code=$(echo "$parsed" | cut -d'|' -f1)
    local body=$(echo "$parsed" | cut -d'|' -f2-)
    
    if [ "$status_code" = "200" ]; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
        if [ "$VERBOSE" = true ]; then
            format_json "$body"
        fi
        return 0
    else
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号] (HTTP $status_code)"
        if [ "$VERBOSE" = true ]; then
            echo "$body"
        fi
        return 1
    fi
}

# [符号][符号][符号][符号][符号][符号]
test_health_check() {
    log_test "[符号][符号][符号][符号]"
    
    local response=$(make_request "GET" "/health")
    local parsed=$(parse_response "$response")
    local status_code=$(echo "$parsed" | cut -d'|' -f1)
    local body=$(echo "$parsed" | cut -d'|' -f2-)
    
    if [ "$status_code" = "200" ]; then
        log_success "[符号][符号][符号][符号][符号][符号]"
        if [ "$VERBOSE" = true ]; then
            format_json "$body"
        fi
        return 0
    else
        log_error "[符号][符号][符号][符号][符号][符号] (HTTP $status_code)"
        if [ "$VERBOSE" = true ]; then
            echo "$body"
        fi
        return 1
    fi
}

# [符号][符号][符号][符号][符号][符号]
test_stats() {
    log_test "[符号][符号][符号][符号]"
    
    local response=$(make_request "GET" "/stats")
    local parsed=$(parse_response "$response")
    local status_code=$(echo "$parsed" | cut -d'|' -f1)
    local body=$(echo "$parsed" | cut -d'|' -f2-)
    
    if [ "$status_code" = "200" ]; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
        if [ "$VERBOSE" = true ]; then
            format_json "$body"
        fi
        return 0
    else
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号] (HTTP $status_code)"
        if [ "$VERBOSE" = true ]; then
            echo "$body"
        fi
        return 1
    fi
}

# [符号][符号][符号][符号][符号][符号]
test_devices() {
    log_test "[符号][符号][符号][符号]"
    
    local response=$(make_request "GET" "/devices")
    local parsed=$(parse_response "$response")
    local status_code=$(echo "$parsed" | cut -d'|' -f1)
    local body=$(echo "$parsed" | cut -d'|' -f2-)
    
    if [ "$status_code" = "200" ]; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
        if [ "$VERBOSE" = true ]; then
            format_json "$body"
        fi
        return 0
    else
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号] (HTTP $status_code)"
        if [ "$VERBOSE" = true ]; then
            echo "$body"
        fi
        return 1
    fi
}

# [符号][符号][符号][符号][符号][符号]
test_single_bypass() {
    log_test "[符号][符号][符号][符号]"
    
    local data='{
        "target_url": "https://httpbin.org/get",
        "method": "GET",
        "strategy": "adaptive",
        "priority": "normal"
    }'
    
    local response=$(make_request "POST" "/bypass/single" "$data")
    local parsed=$(parse_response "$response")
    local status_code=$(echo "$parsed" | cut -d'|' -f1)
    local body=$(echo "$parsed" | cut -d'|' -f2-)
    
    if [ "$status_code" = "200" ]; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
        if [ "$VERBOSE" = true ]; then
            format_json "$body"
        fi
        return 0
    else
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号] (HTTP $status_code)"
        if [ "$VERBOSE" = true ]; then
            echo "$body"
        fi
        return 1
    fi
}

# [符号][符号][符号][符号][符号][符号]
test_batch_bypass() {
    log_test "[符号][符号][符号][符号]"
    
    local data='{
        "requests": [
            {
                "target_url": "https://httpbin.org/get?id=1",
                "method": "GET",
                "strategy": "conservative"
            },
            {
                "target_url": "https://httpbin.org/get?id=2",
                "method": "GET",
                "strategy": "adaptive"
            }
        ],
        "max_concurrency": 2,
        "timeout": 30
    }'
    
    local response=$(make_request "POST" "/bypass/batch" "$data")
    local parsed=$(parse_response "$response")
    local status_code=$(echo "$parsed" | cut -d'|' -f1)
    local body=$(echo "$parsed" | cut -d'|' -f2-)
    
    if [ "$status_code" = "200" ]; then
        log_success "[符号][符号][符号][符号][符号][符号][符号][符号]"
        if [ "$VERBOSE" = true ]; then
            format_json "$body"
        fi
        return 0
    else
        log_error "[符号][符号][符号][符号][符号][符号][符号][符号] (HTTP $status_code)"
        if [ "$VERBOSE" = true ]; then
            echo "$body"
        fi
        return 1
    fi
}

# [符号][符号][符号][符号][符号]
run_tests() {
    echo "================================================================================"
    echo "API[符号][符号][符号][符号] - Shell[符号][符号]"
    echo "================================================================================"
    echo "[符号][符号][符号][符号]: $API_URL"
    echo "[符号][符号][符号][符号]: ${TIMEOUT}[符号]"
    echo ""
    
    local tests=(
        "test_service_info:[符号][符号][符号][符号]"
        "test_health_check:[符号][符号][符号][符号]"
        "test_stats:[符号][符号][符号][符号]"
        "test_devices:[符号][符号][符号][符号]"
        "test_single_bypass:[符号][符号][符号][符号]"
        "test_batch_bypass:[符号][符号][符号][符号]"
    )
    
    local passed=0
    local total=${#tests[@]}
    
    for test_item in "${tests[@]}"; do
        local test_func=$(echo "$test_item" | cut -d':' -f1)
        local test_name=$(echo "$test_item" | cut -d':' -f2)
        
        echo "--------------------------------------------------------------------------------"
        
        if $test_func; then
            log_success "$test_name: [符号][符号]"
            ((passed++))
        else
            log_error "$test_name: [符号][符号]"
        fi
        
        echo ""
        sleep 1
    done
    
    echo "================================================================================"
    echo "[符号][符号][符号][符号][符号][符号]"
    echo "================================================================================"
    echo "[符号][符号][符号][符号]: $total"
    echo "[符号][符号][符号][符号]: $passed"
    echo "[符号][符号][符号][符号]: $((total - passed))"
    echo "[符号][符号][符号]: $(( passed * 100 / total ))%"
    echo "================================================================================"
    
    if [ $passed -eq $total ]; then
        log_success "[符号][符号][符号][符号][符号][符号][符号]API[符号][符号][符号][符号][符号][符号][符号]"
        return 0
    else
        log_warning "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]API[符号][符号][符号][符号][符号]"
        return 1
    fi
}

# [符号][符号][符号]
main() {
    # [符号][符号][符号][符号]
    check_dependencies
    
    # [符号][符号][符号][符号]
    if run_tests; then
        exit 0
    else
        exit 1
    fi
}

# [符号][符号][符号][符号][符号]
main "$@"
