#!/bin/bash

# 最终服务器修复 - 简单可靠的方法
echo "🔧 最终API修复..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 备份当前文件
echo "📦 备份当前文件..."
cp src/core/api_service.py src/core/api_service.py.final_backup.$(date +%Y%m%d_%H%M%S)

# 3. 从最早备份恢复
echo "🔄 从最早备份恢复..."
CLEAN_BACKUP=$(ls -tr src/core/api_service.py.backup.* 2>/dev/null | head -1)
if [ -n "$CLEAN_BACKUP" ]; then
    echo "使用备份: $CLEAN_BACKUP"
    cp "$CLEAN_BACKUP" src/core/api_service.py
else
    echo "❌ 未找到备份文件"
    exit 1
fi

# 4. 使用Python进行安全修复
echo "🔧 使用Python进行安全修复..."
python3 << 'PYTHON_FIX'
import re

# 读取文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

print('🔧 修复 BypassResponse 模型...')

# 1. 修复 BypassResponse 模型定义
if 'strategy_used: Optional[str] = None' not in content:
    # 查找并替换模型定义
    old_pattern = 'timestamp: str\n    response_data: Optional[Dict[str, Any]] = None'
    new_pattern = 'timestamp: str\n    strategy_used: Optional[str] = None\n    modified_fingerprint: Optional[Dict[str, Any]] = None\n    response_data: Optional[Dict[str, Any]] = None'
    
    if old_pattern in content:
        content = content.replace(old_pattern, new_pattern)
        print('  ✅ BypassResponse 模型字段已添加')
    else:
        print('  ⚠️ 模型定义未找到或已修复')
else:
    print('  ℹ️ BypassResponse 模型已包含新字段')

print('🔧 修复响应构建...')

# 2. 修复单次绕过响应构建 - 添加逗号和新字段
old_single = "timestamp=result['timestamp']\n                )"
new_single = "timestamp=result['timestamp'],\n                    strategy_used=result.get('strategy_used'),\n                    modified_fingerprint=None  # 不返回敏感的指纹信息\n                )"

if old_single in content:
    content = content.replace(old_single, new_single)
    print('  ✅ 单次绕过响应构建已修复')
else:
    print('  ℹ️ 单次绕过响应构建已修复或未找到')

# 3. 修复批量绕过成功响应 - 添加逗号和新字段
old_batch_success = "timestamp=result['timestamp']\n                            )"
new_batch_success = "timestamp=result['timestamp'],\n                                strategy_used=result.get('strategy_used'),\n                                modified_fingerprint=None\n                            )"

if old_batch_success in content:
    content = content.replace(old_batch_success, new_batch_success)
    print('  ✅ 批量绕过成功响应已修复')
else:
    print('  ℹ️ 批量绕过成功响应已修复或未找到')

# 4. 修复批量绕过错误响应 - 添加逗号和新字段
old_batch_error = "timestamp=datetime.now().isoformat()\n                            )"
new_batch_error = "timestamp=datetime.now().isoformat(),\n                                strategy_used=None,\n                                modified_fingerprint=None\n                            )"

if old_batch_error in content:
    content = content.replace(old_batch_error, new_batch_error)
    print('  ✅ 批量绕过错误响应已修复')
else:
    print('  ℹ️ 批量绕过错误响应已修复或未找到')

# 写入文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ API修复完成')
PYTHON_FIX

# 5. 验证语法
echo "🔍 验证语法..."
python3 -m py_compile src/core/api_service.py
if [ $? -eq 0 ]; then
    echo "✅ 语法验证通过"
else
    echo "❌ 语法验证失败，显示错误详情："
    python3 -c "
try:
    compile(open('src/core/api_service.py').read(), 'src/core/api_service.py', 'exec')
    print('语法正确')
except SyntaxError as e:
    print(f'语法错误在第 {e.lineno} 行: {e.text.strip() if e.text else \"未知\"}')
    print(f'错误位置: {e.offset}')
    # 显示错误行附近的内容
    with open('src/core/api_service.py') as f:
        lines = f.readlines()
        start = max(0, e.lineno - 3)
        end = min(len(lines), e.lineno + 2)
        for i in range(start, end):
            marker = '>>> ' if i == e.lineno - 1 else '    '
            print(f'{marker}{i+1:3d}: {lines[i].rstrip()}')
"
    echo "恢复到备份..."
    cp "$CLEAN_BACKUP" src/core/api_service.py
    exit 1
fi

# 6. 验证修复内容
echo "🔍 验证修复内容..."
echo "检查BypassResponse模型字段:"
grep -n "strategy_used.*Optional" src/core/api_service.py || echo "  ⚠️ 未找到strategy_used字段定义"
grep -n "modified_fingerprint.*Optional" src/core/api_service.py || echo "  ⚠️ 未找到modified_fingerprint字段定义"

echo "检查响应构建:"
grep -n "strategy_used.*result.get" src/core/api_service.py || echo "  ⚠️ 未找到strategy_used响应构建"
grep -n "modified_fingerprint.*None" src/core/api_service.py || echo "  ⚠️ 未找到modified_fingerprint响应构建"

# 7. 重启服务
echo "🚀 重启服务..."
sudo supervisorctl start starbucks_bypass

# 8. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 9. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 10. 测试API
echo "🧪 测试单次绕过API..."
response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$response" | head -3
    echo ""
    echo "🎉 修复成功！现在运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$response"
    echo ""
    echo "⚠️ 需要进一步调试，检查服务日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi
