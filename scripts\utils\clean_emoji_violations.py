#!/usr/bin/env python3
"""
清理代码中的emoji表情符号
严格按照代码开发规范，移除所有emoji表情
"""

import os
import re
from pathlib import Path
from typing import Dict, List

class EmojiCleaner:
    """Emoji清理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.cleaned_files = []
        self.total_emojis_removed = 0
        
        # emoji替换映射
        self.emoji_replacements = {
            # 状态类
            '🚨': '[紧急]',
            '🛑': '[停止]',
            '✅': '[成功]',
            '❌': '[错误]',
            '⚠️': '[警告]',
            '⚠': '[警告]',
            'ℹ️': '[信息]',
            'ℹ': '[信息]',
            
            # 动作类
            '🚀': '[启动]',
            '🔍': '[检查]',
            '🔧': '[修复]',
            '🧪': '[测试]',
            '📦': '[包]',
            '📝': '[记录]',
            '📊': '[统计]',
            '📈': '[图表]',
            
            # 时间类
            '⏳': '[等待]',
            '⏰': '[时间]',
            
            # 庆祝类
            '🎉': '[完成]',
            '🎊': '[庆祝]',
            '👍': '[好]',
            '👎': '[差]',
            
            # 其他常见emoji
            '💡': '[想法]',
            '🔥': '[热门]',
            '💯': '[百分百]',
            '🌟': '[星级]',
            '⭐': '[星]',
            '🔑': '[关键]',
            '🎯': '[目标]',
            '📋': '[清单]',
            '📌': '[标记]',
            '🔗': '[链接]',
            '📁': '[文件夹]',
            '📄': '[文档]',
            '🖥️': '[电脑]',
            '🖥': '[电脑]',
            '💻': '[笔记本]',
            '📱': '[手机]',
            '🌐': '[网络]',
            '🔒': '[锁定]',
            '🔓': '[解锁]',
            '🛠️': '[工具]',
            '🛠': '[工具]',
            '⚙️': '[设置]',
            '⚙': '[设置]',
            '🔄': '[刷新]',
            '🔃': '[循环]',
            '🔁': '[重复]',
            '▶️': '[播放]',
            '▶': '[播放]',
            '⏸️': '[暂停]',
            '⏸': '[暂停]',
            '⏹️': '[停止]',
            '⏹': '[停止]',
            '⏭️': '[下一个]',
            '⏭': '[下一个]',
            '⏮️': '[上一个]',
            '⏮': '[上一个]',
        }
        
        # 构建emoji检测模式
        self.emoji_pattern = re.compile(
            r'[\U0001F600-\U0001F64F]|'  # 表情符号
            r'[\U0001F300-\U0001F5FF]|'  # 符号和象形文字
            r'[\U0001F680-\U0001F6FF]|'  # 交通和地图符号
            r'[\U0001F1E0-\U0001F1FF]|'  # 国旗
            r'[\U00002702-\U000027B0]|'  # 杂项符号
            r'[\U000024C2-\U0001F251]'   # 其他符号
        )
    
    def clean_emoji_in_text(self, text: str) -> tuple[str, int]:
        """清理文本中的emoji"""
        original_text = text
        emoji_count = 0
        
        # 替换已知的emoji
        for emoji, replacement in self.emoji_replacements.items():
            if emoji in text:
                text = text.replace(emoji, replacement)
                emoji_count += original_text.count(emoji)
        
        # 移除其他未知emoji
        remaining_emojis = self.emoji_pattern.findall(text)
        if remaining_emojis:
            text = self.emoji_pattern.sub('[符号]', text)
            emoji_count += len(remaining_emojis)
        
        return text, emoji_count
    
    def clean_file(self, file_path: Path) -> bool:
        """清理单个文件"""
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 清理emoji
            cleaned_content, emoji_count = self.clean_emoji_in_text(content)
            
            # 如果有变化，写回文件
            if emoji_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                
                self.cleaned_files.append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'emoji_count': emoji_count
                })
                self.total_emojis_removed += emoji_count
                print(f"清理 {file_path.relative_to(self.project_root)}: {emoji_count} 个emoji")
                return True
            
            return False
            
        except Exception as e:
            print(f"清理文件失败 {file_path}: {e}")
            return False
    
    def clean_project(self):
        """清理整个项目"""
        print("开始清理项目中的emoji表情...")
        print("=" * 60)
        
        # 要清理的文件类型
        file_patterns = ['*.py', '*.sh']
        
        # 排除的目录
        excluded_dirs = {'__pycache__', '.git', 'node_modules', 'venv', 'env'}
        
        cleaned_count = 0
        total_files = 0
        
        for pattern in file_patterns:
            for file_path in self.project_root.rglob(pattern):
                # 跳过排除的目录
                if any(excluded_dir in file_path.parts for excluded_dir in excluded_dirs):
                    continue
                
                total_files += 1
                if self.clean_file(file_path):
                    cleaned_count += 1
        
        print("\n" + "=" * 60)
        print("清理完成统计:")
        print(f"总文件数: {total_files}")
        print(f"清理文件数: {cleaned_count}")
        print(f"移除emoji总数: {self.total_emojis_removed}")
        
        if self.cleaned_files:
            print("\n清理详情:")
            for file_info in self.cleaned_files:
                print(f"  {file_info['file']}: {file_info['emoji_count']} 个emoji")
        
        # 生成清理报告
        self.generate_report()
    
    def generate_report(self):
        """生成清理报告"""
        report_path = self.project_root / "emoji_cleanup_report.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Emoji清理报告\n\n")
            f.write(f"清理时间: {__import__('datetime').datetime.now()}\n\n")
            f.write("## 清理统计\n\n")
            f.write(f"- 清理文件数: {len(self.cleaned_files)}\n")
            f.write(f"- 移除emoji总数: {self.total_emojis_removed}\n\n")
            
            if self.cleaned_files:
                f.write("## 清理详情\n\n")
                for file_info in self.cleaned_files:
                    f.write(f"- {file_info['file']}: {file_info['emoji_count']} 个emoji\n")
            
            f.write("\n## Emoji替换规则\n\n")
            for emoji, replacement in self.emoji_replacements.items():
                f.write(f"- {emoji} → {replacement}\n")
        
        print(f"\n清理报告已保存到: {report_path}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='清理项目中的emoji表情符号')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    parser.add_argument('--dry-run', action='store_true', help='只检查不修改')
    
    args = parser.parse_args()
    
    cleaner = EmojiCleaner(args.project_root)
    
    if args.dry_run:
        print("干运行模式: 只检查不修改文件")
        # 这里可以添加干运行逻辑
    else:
        cleaner.clean_project()

if __name__ == "__main__":
    main()
