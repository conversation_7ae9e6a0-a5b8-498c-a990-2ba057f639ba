#!/bin/bash

# 精确修复服务器API服务文件的导入问题
echo "🔧 精确修复服务器API服务文件导入问题..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 4. 备份当前文件
echo "📦 备份当前文件..."
cp src/core/api_service.py src/core/api_service.py.backup.$(date +%Y%m%d_%H%M%S)

# 5. 检查当前错误的导入
echo "🔍 检查当前错误的导入..."
echo "=== 当前的导入语句 ==="
grep "^from " src/core/api_service.py | head -10

# 6. 使用Python脚本精确修复导入
echo "🔧 使用Python脚本精确修复导入..."
python3 << 'PYTHON_FIX'
import re

# 读取文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 精确替换导入语句
replacements = [
    # 修复config_manager导入
    (r'from utils\.config_manager import', 'from config.config_manager import'),
    # 修复其他可能的错误导入
    (r'from \.\.utils\.', 'from utils.'),
    (r'from \.\.config\.', 'from config.'),
    (r'from \.device_fingerprint_engine', 'from core.device_fingerprint_engine'),
    (r'from \.bypass_engine', 'from core.bypass_engine'),
    (r'from \.concurrency_controller', 'from core.concurrency_controller'),
    (r'from \.monitor', 'from core.monitor'),
    (r'from \.time_scheduler', 'from core.time_scheduler'),
    (r'from \.header_generator', 'from core.header_generator'),
    (r'from \.http_engine', 'from core.http_engine'),
    (r'from \.result_analyzer', 'from core.result_analyzer'),
    (r'from \.device_manager', 'from core.device_manager'),
]

# 应用所有替换
for pattern, replacement in replacements:
    content = re.sub(pattern, replacement, content)

# 写回文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ Python脚本修复完成')
PYTHON_FIX

# 7. 检查修复后的导入
echo "🔍 检查修复后的导入..."
echo "=== 修复后的导入语句 ==="
grep "^from " src/core/api_service.py | head -10

# 8. 验证语法
echo "🔍 验证语法..."
cd src
python3 -c "
import ast
with open('core/api_service.py', 'r') as f:
    content = f.read()
try:
    ast.parse(content)
    print('✅ 语法验证通过')
except SyntaxError as e:
    print(f'❌ 语法错误: 第{e.lineno}行: {e.text.strip() if e.text else \"未知\"}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 语法验证失败"
    deactivate
    exit 1
fi

# 9. 逐个测试模块导入
echo "🔍 逐个测试模块导入..."

echo "测试 utils.logger:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from utils.logger import get_logger
    print('✅ utils.logger 导入成功')
except Exception as e:
    print(f'❌ utils.logger 导入失败: {e}')
    exit(1)
"

echo "测试 config.config_manager:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from config.config_manager import ConfigManager
    print('✅ config.config_manager 导入成功')
except Exception as e:
    print(f'❌ config.config_manager 导入失败: {e}')
    exit(1)
"

echo "测试 core.device_fingerprint_engine:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.device_fingerprint_engine import DeviceFingerprintEngine
    print('✅ core.device_fingerprint_engine 导入成功')
except Exception as e:
    print(f'❌ core.device_fingerprint_engine 导入失败: {e}')
    exit(1)
"

echo "测试 core.bypass_engine:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.bypass_engine import BypassEngine
    print('✅ core.bypass_engine 导入成功')
except Exception as e:
    print(f'❌ core.bypass_engine 导入失败: {e}')
    exit(1)
"

echo "测试 core.api_service:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.api_service import app, create_app
    print('✅ core.api_service 导入成功')
except Exception as e:
    print(f'❌ core.api_service 导入失败: {e}')
    exit(1)
"

# 10. 启动服务
echo "🚀 启动服务..."
cd /home/<USER>/apps/starbucks_bypass_tester
sudo supervisorctl start starbucks_bypass

# 11. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 12. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 13. 检查启动日志
echo "📋 检查启动日志..."
echo "=== 最近的输出日志 ==="
tail -15 logs/output.log 2>/dev/null || echo "无输出日志"
echo ""
echo "=== 最近的错误日志 ==="
tail -10 logs/error.log 2>/dev/null || echo "无错误日志"

# 14. 测试所有API接口
echo "🧪 测试所有API接口..."

# 健康检查
echo "1. 测试健康检查:"
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
    echo "$health_response"
else
    echo "❌ 健康检查失败: $health_response"
fi

# 信息接口
echo ""
echo "2. 测试信息接口:"
info_response=$(curl -s http://localhost:8000/info 2>/dev/null)
if echo "$info_response" | grep -q '"name"'; then
    echo "✅ 信息接口通过"
    echo "$info_response"
else
    echo "❌ 信息接口失败: $info_response"
fi

# 设备接口
echo ""
echo "3. 测试设备接口:"
devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null)
if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ 设备接口通过"
    echo "设备数量: $(echo "$devices_response" | grep -o '"total":[0-9]*' | cut -d: -f2)"
else
    echo "❌ 设备接口失败: $devices_response"
fi

# 统计接口
echo ""
echo "4. 测试统计接口:"
stats_response=$(curl -s http://localhost:8000/stats 2>/dev/null)
if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ 统计接口通过"
else
    echo "❌ 统计接口失败: $stats_response"
fi

# 单次绕过
echo ""
echo "5. 测试单次绕过:"
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
fi

# 批量绕过
echo ""
echo "6. 测试批量绕过:"
batch_response=$(curl -s -X POST http://localhost:8000/bypass/batch \
  -H "Content-Type: application/json" \
  -d '{
    "requests": [
      {
        "target_url": "https://httpbin.org/get",
        "method": "GET",
        "strategy": "adaptive"
      }
    ]
  }' 2>/dev/null)

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ 批量绕过API测试成功"
    echo "响应预览:"
    echo "$batch_response" | head -3
else
    echo "❌ 批量绕过API测试失败"
    echo "错误响应:"
    echo "$batch_response"
fi

# 15. 统计测试结果
echo ""
echo "🎯 API测试总结:"
echo "================================"

success_count=0
total_count=6

# 检查各个接口
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ /health - 通过"
    ((success_count++))
else
    echo "❌ /health - 失败"
fi

if echo "$info_response" | grep -q '"name"'; then
    echo "✅ /info - 通过"
    ((success_count++))
else
    echo "❌ /info - 失败"
fi

if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ /devices - 通过"
    ((success_count++))
else
    echo "❌ /devices - 失败"
fi

if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ /stats - 通过"
    ((success_count++))
else
    echo "❌ /stats - 失败"
fi

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ /bypass/single - 通过"
    ((success_count++))
else
    echo "❌ /bypass/single - 失败"
fi

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ /bypass/batch - 通过"
    ((success_count++))
else
    echo "❌ /bypass/batch - 失败"
fi

echo "================================"
echo "通过率: $success_count/$total_count ($(( success_count * 100 / total_count ))%)"

if [ $success_count -eq $total_count ]; then
    echo ""
    echo "🎉 所有API测试通过！服务修复成功！"
    echo "现在可以运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo ""
    echo "⚠️ 部分API测试失败，需要进一步调试"
    echo "查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
