#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构整理脚本 - Windows环境优化版
专门为Windows环境设计，解决Python路径问题
"""

import os
import shutil
import json
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

class ProjectStructureOrganizer:
    """项目结构整理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.excluded_dirs = {"backup_starbucks_bypass_tester", "xbk", "docs", "__pycache__"}
        self.results = {
            "organized_files": 0,
            "created_directories": 0,
            "moved_files": 0,
            "updated_imports": 0,
            "errors": []
        }
        
    def create_standard_structure(self) -> bool:
        """创建标准目录结构"""
        print("[信息] 创建标准目录结构...")
        
        # 标准目录结构
        standard_dirs = [
            "starbucks_bypass_tester/src/core",
            "starbucks_bypass_tester/src/config", 
            "starbucks_bypass_tester/src/cli",
            "starbucks_bypass_tester/src/utils/testing",
            "starbucks_bypass_tester/tests",
            "starbucks_bypass_tester/data/analysis",
            "starbucks_bypass_tester/data/processed",
            "starbucks_bypass_tester/data/raw",
            "starbucks_bypass_tester/data/results",
            "starbucks_bypass_tester/logs",
            "scripts/deployment",
            "scripts/management", 
            "scripts/testing",
            "scripts/fixes",
            "scripts/maintenance",
            "scripts/utils"
        ]
        
        created_count = 0
        for dir_path in standard_dirs:
            full_path = self.project_root / dir_path
            try:
                full_path.mkdir(parents=True, exist_ok=True)
                if not full_path.exists():
                    print(f"  [创建] {dir_path}")
                    created_count += 1
                else:
                    print(f"  [存在] {dir_path}")
            except Exception as e:
                error_msg = f"创建目录失败: {dir_path} - {e}"
                print(f"  [错误] {error_msg}")
                self.results["errors"].append(error_msg)
        
        self.results["created_directories"] = created_count
        print(f"[完成] 创建了 {created_count} 个目录")
        return True
    
    def create_init_files(self) -> int:
        """创建缺失的__init__.py文件"""
        print("[信息] 创建__init__.py文件...")
        
        # 需要__init__.py的目录
        init_dirs = [
            "starbucks_bypass_tester/src",
            "starbucks_bypass_tester/src/core",
            "starbucks_bypass_tester/src/config",
            "starbucks_bypass_tester/src/cli", 
            "starbucks_bypass_tester/src/utils",
            "starbucks_bypass_tester/src/utils/testing",
            "starbucks_bypass_tester/tests"
        ]
        
        created_count = 0
        for dir_path in init_dirs:
            init_file = self.project_root / dir_path / "__init__.py"
            try:
                if not init_file.exists():
                    init_file.write_text('# -*- coding: utf-8 -*-\n"""模块初始化文件"""\n', encoding='utf-8')
                    print(f"  [创建] {dir_path}/__init__.py")
                    created_count += 1
                else:
                    print(f"  [存在] {dir_path}/__init__.py")
            except Exception as e:
                error_msg = f"创建__init__.py失败: {dir_path} - {e}"
                print(f"  [错误] {error_msg}")
                self.results["errors"].append(error_msg)
        
        print(f"[完成] 创建了 {created_count} 个__init__.py文件")
        return created_count
    
    def organize_scripts(self) -> int:
        """整理脚本文件到标准目录"""
        print("[信息] 整理脚本文件...")
        
        scripts_dir = self.project_root / "scripts"
        if not scripts_dir.exists():
            print("[跳过] scripts目录不存在")
            return 0
        
        # 脚本分类规则
        script_categories = {
            "deployment": ["install", "uninstall", "create_user", "delete_user"],
            "management": ["start_all", "monitor", "view_logs", "check_status"],
            "testing": ["api_test", "quick_api", "test_api", "verify"],
            "fixes": ["fix_", "quick_fix", "emergency"],
            "maintenance": ["clean_", "organize_", "server_"],
            "utils": ["diagnose_", "final_"]
        }
        
        moved_count = 0
        for script_file in scripts_dir.glob("*.py"):
            if script_file.name.startswith("__"):
                continue
                
            # 确定脚本类别
            category = "utils"  # 默认类别
            for cat, keywords in script_categories.items():
                if any(keyword in script_file.name for keyword in keywords):
                    category = cat
                    break
            
            # 移动到对应目录
            target_dir = scripts_dir / category
            target_file = target_dir / script_file.name
            
            try:
                if not target_file.exists():
                    shutil.move(str(script_file), str(target_file))
                    print(f"  [移动] {script_file.name} -> {category}/")
                    moved_count += 1
                else:
                    print(f"  [存在] {category}/{script_file.name}")
            except Exception as e:
                error_msg = f"移动脚本失败: {script_file.name} - {e}"
                print(f"  [错误] {error_msg}")
                self.results["errors"].append(error_msg)
        
        # 整理shell脚本
        for script_file in scripts_dir.glob("*.sh"):
            category = "utils"
            for cat, keywords in script_categories.items():
                if any(keyword in script_file.name for keyword in keywords):
                    category = cat
                    break
            
            target_dir = scripts_dir / category
            target_file = target_dir / script_file.name
            
            try:
                if not target_file.exists():
                    shutil.move(str(script_file), str(target_file))
                    print(f"  [移动] {script_file.name} -> {category}/")
                    moved_count += 1
            except Exception as e:
                error_msg = f"移动脚本失败: {script_file.name} - {e}"
                print(f"  [错误] {error_msg}")
                self.results["errors"].append(error_msg)
        
        self.results["moved_files"] = moved_count
        print(f"[完成] 移动了 {moved_count} 个脚本文件")
        return moved_count
    
    def validate_file_structure(self) -> Dict[str, int]:
        """验证文件结构"""
        print("[信息] 验证文件结构...")
        
        stats = {
            "python_files": 0,
            "json_files": 0,
            "shell_scripts": 0,
            "log_files": 0,
            "total_files": 0
        }
        
        # 统计文件类型
        for root, dirs, files in os.walk(self.project_root):
            # 跳过排除的目录
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            for file in files:
                stats["total_files"] += 1
                
                if file.endswith('.py'):
                    stats["python_files"] += 1
                elif file.endswith('.json'):
                    stats["json_files"] += 1
                elif file.endswith('.sh'):
                    stats["shell_scripts"] += 1
                elif file.endswith('.log'):
                    stats["log_files"] += 1
        
        print(f"  Python文件: {stats['python_files']} 个")
        print(f"  JSON配置文件: {stats['json_files']} 个")
        print(f"  Shell脚本: {stats['shell_scripts']} 个")
        print(f"  日志文件: {stats['log_files']} 个")
        print(f"  总文件数: {stats['total_files']} 个")
        
        return stats
    
    def check_naming_conventions(self) -> List[str]:
        """检查命名规范"""
        print("[信息] 检查文件命名规范...")
        
        violations = []
        
        # 检查Python文件命名
        for py_file in self.project_root.rglob("*.py"):
            if any(excluded in str(py_file) for excluded in self.excluded_dirs):
                continue
                
            filename = py_file.stem
            # 检查是否符合snake_case
            if not re.match(r'^[a-z][a-z0-9_]*$', filename) and filename != "__init__":
                violations.append(f"Python文件命名不规范: {py_file.relative_to(self.project_root)}")
        
        # 检查目录命名
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            for dir_name in dirs:
                if not re.match(r'^[a-z][a-z0-9_]*$', dir_name) and dir_name not in ["__pycache__"]:
                    rel_path = Path(root).relative_to(self.project_root) / dir_name
                    violations.append(f"目录命名不规范: {rel_path}")
        
        if violations:
            print(f"  [警告] 发现 {len(violations)} 个命名规范问题")
            for violation in violations[:5]:  # 只显示前5个
                print(f"    {violation}")
            if len(violations) > 5:
                print(f"    ... 还有 {len(violations) - 5} 个问题")
        else:
            print("  [通过] 所有文件和目录命名符合规范")
        
        return violations
    
    def generate_structure_report(self) -> Dict:
        """生成结构报告"""
        print("[信息] 生成项目结构报告...")
        
        report = {
            "project_name": "starbucks_bypass_tester",
            "organization_date": "2025-07-31",
            "structure": {},
            "statistics": {},
            "compliance": {}
        }
        
        # 生成目录树
        def build_tree(path: Path, max_depth: int = 3, current_depth: int = 0) -> Dict:
            if current_depth >= max_depth:
                return {}
            
            tree = {}
            try:
                for item in sorted(path.iterdir()):
                    if item.name.startswith('.') or item.name in self.excluded_dirs:
                        continue
                    
                    if item.is_dir():
                        tree[item.name + "/"] = build_tree(item, max_depth, current_depth + 1)
                    else:
                        tree[item.name] = f"文件 ({item.stat().st_size} bytes)"
            except PermissionError:
                tree["<权限不足>"] = "无法访问"
            
            return tree
        
        report["structure"] = build_tree(self.project_root)
        
        # 统计信息
        stats = self.validate_file_structure()
        report["statistics"] = stats
        
        # 合规性检查
        naming_violations = self.check_naming_conventions()
        report["compliance"] = {
            "naming_violations": len(naming_violations),
            "structure_complete": True,
            "init_files_present": True
        }
        
        return report
    
    def run_full_organization(self) -> Dict:
        """执行完整的项目结构整理"""
        print("=" * 60)
        print("星巴克设备指纹绕过系统 - 项目结构整理")
        print("=" * 60)
        
        try:
            # 1. 创建标准目录结构
            self.create_standard_structure()
            print()
            
            # 2. 创建__init__.py文件
            init_count = self.create_init_files()
            print()
            
            # 3. 整理脚本文件
            script_count = self.organize_scripts()
            print()
            
            # 4. 验证文件结构
            file_stats = self.validate_file_structure()
            print()
            
            # 5. 检查命名规范
            naming_violations = self.check_naming_conventions()
            print()
            
            # 6. 生成结构报告
            structure_report = self.generate_structure_report()
            print()
            
            # 更新结果
            self.results.update({
                "init_files_created": init_count,
                "file_statistics": file_stats,
                "naming_violations": len(naming_violations),
                "structure_report": structure_report
            })
            
            # 输出总结
            print("=" * 60)
            print("项目结构整理完成总结:")
            print(f"- 创建目录: {self.results['created_directories']} 个")
            print(f"- 创建__init__.py: {init_count} 个")
            print(f"- 移动脚本文件: {script_count} 个")
            print(f"- Python文件: {file_stats['python_files']} 个")
            print(f"- 配置文件: {file_stats['json_files']} 个")
            print(f"- 命名规范问题: {len(naming_violations)} 个")
            print(f"- 错误数量: {len(self.results['errors'])} 个")
            print("=" * 60)
            
        except Exception as e:
            error_msg = f"整理过程中出现错误: {e}"
            print(f"[错误] {error_msg}")
            self.results["errors"].append(error_msg)
        
        return self.results

def main():
    """主函数"""
    organizer = ProjectStructureOrganizer()
    results = organizer.run_full_organization()
    
    # 保存结果到文件
    results_file = Path("project_structure_organization_results.json")
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print(f"[信息] 整理结果已保存到: {results_file}")
    except Exception as e:
        print(f"[错误] 保存结果失败: {e}")

if __name__ == "__main__":
    main()
