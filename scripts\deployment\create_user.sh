#!/bin/bash

# 星巴克设备指纹绕过系统 - 用户创建脚本
# 用途: 为root用户提供创建普通用户的脚本，用于后续安装

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo ""
        echo "请使用以下方式运行:"
        echo "  sudo ./scripts/create_user.sh"
        echo "  或"
        echo "  su -c './scripts/create_user.sh'"
        exit 1
    fi
}

# 创建管理用户
create_admin_user() {
    local username="$1"
    local password="$2"
    
    log_info "创建管理用户: $username"
    
    # 检查用户是否已存在
    if id "$username" >/dev/null 2>&1; then
        log_warning "用户 $username 已存在"
        echo ""
        echo "用户信息:"
        id "$username"
        echo ""
        
        read -p "是否继续使用现有用户? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作取消"
            exit 0
        fi
        
        log_info "使用现有用户: $username"
    else
        log_info "创建新用户: $username"
        
        # 创建用户
        useradd -m -s /bin/bash "$username"
        
        # 设置密码
        echo "$username:$password" | chpasswd
        
        # 添加到sudo组
        usermod -aG sudo "$username"
        
        log_success "用户创建成功: $username"
    fi
    
    # 创建SSH目录
    local user_home="/home/<USER>"
    mkdir -p "$user_home/.ssh"
    chown "$username:$username" "$user_home/.ssh"
    chmod 700 "$user_home/.ssh"
    
    # 复制root的SSH密钥（如果存在）
    if [ -f "/root/.ssh/authorized_keys" ]; then
        read -p "是否复制root的SSH密钥到新用户? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cp "/root/.ssh/authorized_keys" "$user_home/.ssh/"
            chown "$username:$username" "$user_home/.ssh/authorized_keys"
            chmod 600 "$user_home/.ssh/authorized_keys"
            log_success "SSH密钥已复制"
        fi
    fi
    
    # 验证sudo权限
    if sudo -u "$username" sudo -n true 2>/dev/null; then
        log_success "用户 $username 具有sudo权限"
    else
        log_info "用户 $username 需要密码进行sudo操作"
    fi

    # 复制项目文件到用户主目录
    log_info "复制项目文件到用户主目录..."
    local user_home="/home/<USER>"
    local project_dir="$user_home/starbucks_bypass_project"

    # 获取当前脚本所在的项目根目录
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local current_project_root="$(dirname "$script_dir")"

    log_info "项目根目录: $current_project_root"

    # 创建项目目录
    mkdir -p "$project_dir"

    # 检查并复制项目的两个主要目录
    if [ -d "$current_project_root/scripts" ]; then
        log_info "复制scripts目录..."
        cp -r "$current_project_root/scripts" "$project_dir/"
    else
        log_error "未找到scripts目录: $current_project_root/scripts"
    fi

    if [ -d "$current_project_root/starbucks_bypass_tester" ]; then
        log_info "复制starbucks_bypass_tester目录..."
        cp -r "$current_project_root/starbucks_bypass_tester" "$project_dir/"
    else
        log_error "未找到starbucks_bypass_tester目录: $current_project_root/starbucks_bypass_tester"
    fi

    # 复制其他可能的文件
    for item in "$current_project_root"/*; do
        local basename=$(basename "$item")
        if [ "$basename" != "scripts" ] && [ "$basename" != "starbucks_bypass_tester" ] && [ -f "$item" ]; then
            log_info "复制文件: $basename"
            cp "$item" "$project_dir/"
        fi
    done

    # 设置权限
    chown -R "$username:$username" "$project_dir"
    chmod +x "$project_dir/scripts/"*.sh 2>/dev/null || true

    # 验证复制结果
    echo ""
    log_info "复制结果验证:"
    echo "  项目目录: $project_dir"
    echo "  scripts目录: $([ -d "$project_dir/scripts" ] && echo "存在" || echo "不存在")"
    echo "  starbucks_bypass_tester目录: $([ -d "$project_dir/starbucks_bypass_tester" ] && echo "存在" || echo "不存在")"

    if [ -d "$project_dir/scripts" ] && [ -d "$project_dir/starbucks_bypass_tester" ]; then
        log_success "项目文件复制完成: $project_dir"
    else
        log_error "项目文件复制不完整，请检查源目录结构"
    fi

    echo ""
    log_success "用户 $username 配置完成"
}

# 显示使用说明
show_usage_info() {
    local username="$1"
    local password="$2"
    
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  用户创建完成${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    echo -e "${GREEN}🔑 用户信息:${NC}"
    echo "  用户名: $username"
    echo "  密码: $password"
    echo "  主目录: /home/<USER>"
    echo "  权限: sudo权限已配置"
    echo "  用途: 运行脚本 + 运行应用"
    echo ""
    
    echo -e "${CYAN}📋 下一步操作:${NC}"
    echo ""
    echo "1. 切换到用户:"
    echo "   su - $username"
    echo ""
    echo "2. 或者SSH登录 (如果配置了SSH):"
    echo "   ssh $username@your-server-ip"
    echo ""
    echo "3. 进入项目目录:"
    echo "   cd ~/starbucks_bypass_project"
    echo ""
    echo "4. 运行安装脚本:"
    echo "   ./scripts/install_ubuntu.sh"
    echo ""
    echo "5. 完全卸载 (不留痕迹):"
    echo "   ./scripts/uninstall_ubuntu.sh"
    echo "   (会删除用户 $username 和所有数据)"
    echo ""
    
    echo -e "${YELLOW}[警告]  重要提醒:${NC}"
    echo "  - 用户密码: $password"
    echo "  - 安装脚本必须使用用户 $username 运行"
    echo "  - 应用将在用户 $username 下运行"
    echo "  - 卸载时会完全删除用户 $username"
    echo "  - 系统将恢复到安装前状态，不留任何痕迹"
    echo ""
    
    echo -e "${BLUE}📁 期望的项目结构:${NC}"
    echo "  项目根目录/"
    echo "  ├── scripts/"
    echo "  │   ├── install_ubuntu.sh"
    echo "  │   └── uninstall_ubuntu.sh"
    echo "  └── starbucks_bypass_tester/"
    echo "      ├── main.py"
    echo "      ├── src/"
    echo "      └── requirements.txt"
    echo ""
}

# 主函数
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  星巴克设备指纹绕过系统 - 用户创建${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # 检查root权限
    check_root
    
    echo -e "${CYAN}此脚本将创建用户用于运行安装脚本和应用程序${NC}"
    echo ""
    echo "说明:"
    echo "  - 安装脚本禁止root用户直接执行"
    echo "  - 创建与install/uninstall脚本完全一致的用户"
    echo "  - 卸载时会完全删除此用户，不留任何痕迹"
    echo "  - 确保系统的完整性和安全性"
    echo ""
    
    # 用户配置 - 与install/uninstall脚本完全一致
    # 固定用户配置，确保与install/uninstall脚本一致
    local admin_username="starbucks"  # 使用相同的用户名
    local admin_password="Starbucks@2025"  # 使用相同的密码

    echo -e "${YELLOW}用户配置说明:${NC}"
    echo ""
    echo "为确保与安装/卸载脚本完全一致，系统将创建:"
    echo ""
    echo "🔑 统一用户配置:"
    echo "  用户名: $admin_username"
    echo "  密码: $admin_password"
    echo "  用途: 运行安装脚本 + 运行应用程序"
    echo ""
    echo "📋 用户职责:"
    echo "  1. 运行安装/卸载脚本"
    echo "  2. 运行星巴克绕过应用"
    echo "  3. 系统管理和维护"
    echo ""
    echo "🗑️ 卸载清理:"
    echo "  - uninstall脚本会完全删除此用户"
    echo "  - 删除所有相关文件和配置"
    echo "  - 系统恢复到安装前状态"
    echo ""

    username="$admin_username"
    password="$admin_password"
    
    echo ""
    echo -e "${CYAN}将要创建的用户:${NC}"
    echo "  用户名: $username"
    echo "  密码: $password"
    echo "  权限: sudo权限"
    echo "  用途: 运行脚本 + 运行应用"
    echo ""
    echo -e "${YELLOW}[警告]  重要说明:${NC}"
    echo "  - 此用户与install/uninstall脚本完全一致"
    echo "  - 卸载时会完全删除此用户"
    echo "  - 确保系统完全不留痕迹"
    echo ""
    
    # 确认创建
    read -p "确认创建用户? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作取消"
        exit 0
    fi
    
    echo ""
    log_info "开始创建用户..."
    echo ""
    
    # 创建用户
    create_admin_user "$username" "$password"
    
    # 显示使用说明
    show_usage_info "$username" "$password"
    
    echo -e "${GREEN}用户创建完成！现在可以使用新用户运行安装脚本了。${NC}"
}

# 执行主函数
main "$@"
