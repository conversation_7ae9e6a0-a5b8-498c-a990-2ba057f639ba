#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."

# 1. [符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号]..."
sudo supervisorctl stop starbucks_bypass

# 2. [符号][符号][符号][符号][符号][符号][符号]
echo "[[符号]] [符号][符号][符号][符号][符号][符号]..."
if [ -f "src/core/api_service.py.backup" ]; then
    echo "[符号][符号] api_service.py [符号][符号][符号]..."
    cp src/core/api_service.py.backup src/core/api_service.py
else
    echo "[[符号][符号]] [符号][符号][符号][符号] api_service.py.backup"
fi

if [ -f "src/core/bypass_engine.py.backup" ]; then
    echo "[符号][符号] bypass_engine.py [符号][符号][符号]..."
    cp src/core/bypass_engine.py.backup src/core/bypass_engine.py
else
    echo "[[符号][符号]] [符号][符号][符号][符号] bypass_engine.py.backup"
fi

if [ -f "src/core/device_fingerprint_engine.py.backup" ]; then
    echo "[符号][符号] device_fingerprint_engine.py [符号][符号][符号]..."
    cp src/core/device_fingerprint_engine.py.backup src/core/device_fingerprint_engine.py
else
    echo "[[符号][符号]] [符号][符号][符号][符号] device_fingerprint_engine.py.backup"
fi

# 3. [符号][符号][符号][符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]..."
cat > complete_fix.py << 'PYTHON_SCRIPT_END'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def read_file(filepath):
    """[符号][符号][符号][符号][符号][符号]"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号] {filepath}: {e}")
        return None

def write_file(filepath, content):
    """[符号][符号][符号][符号][符号][符号]"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"[[符号][符号]] [符号][符号][符号][符号][符号][符号] {filepath}: {e}")
        return False

def fix_bypass_engine():
    """[符号][符号] bypass_engine.py"""
    filepath = 'src/core/bypass_engine.py'
    print(f"[[符号][符号]] [符号][符号] {filepath}...")
    
    content = read_file(filepath)
    if content is None:
        return False
    
    original_content = content
    
    # [符号][符号]1: DeviceFingerprintEngine [符号][符号][符号][符号]
    if "fingerprint_engine: DeviceFingerprintEngine = None" in content:
        content = content.replace(
            "fingerprint_engine: DeviceFingerprintEngine = None",
            "fingerprint_engine: 'DeviceFingerprintEngine' = None"
        )
        print("  [[符号][符号]] [符号][符号] DeviceFingerprintEngine [符号][符号][符号][符号]")
    
    # [符号][符号]2: DeviceFingerprint [符号][符号][符号][符号]
    if "fingerprint: DeviceFingerprint) -> float:" in content:
        content = content.replace(
            "fingerprint: DeviceFingerprint) -> float:",
            "fingerprint: 'DeviceFingerprint') -> float:"
        )
        print("  [[符号][符号]] [符号][符号] DeviceFingerprint [符号][符号][符号][符号]")
    
    if content != original_content:
        return write_file(filepath, content)
    else:
        print("  [[符号][符号]] [符号][符号][符号][符号]")
        return True

def fix_api_service():
    """[符号][符号] api_service.py"""
    filepath = 'src/core/api_service.py'
    print(f"[[符号][符号]] [符号][符号] {filepath}...")
    
    content = read_file(filepath)
    if content is None:
        return False
    
    original_content = content
    
    # [符号][符号]1: [符号][符号][符号]
    if "self.bypass_engine.get_bypass_stats()" in content:
        content = content.replace(
            "self.bypass_engine.get_bypass_stats()",
            "await self.bypass_engine.get_bypass_statistics()"
        )
        print("  [[符号][符号]] [符号][符号][符号][符号][符号] get_bypass_stats -> get_bypass_statistics")
    
    # [符号][符号]2: [符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    lines = content.split('\n')
    modified = False
    
    for i, line in enumerate(lines):
        # [符号][符号][符号][符号][符号] data= [符号][符号][符号][符号]
        if "request.data," in line and "data=request.data," not in line:
            lines[i] = line.replace("request.data,", "data=request.data,")
            print(f"  [[符号][符号]] [符号][符号][符号]{i+1}[符号]: request.data [符号][符号]")
            modified = True
        
        if "bypass_req.data," in line and "data=bypass_req.data," not in line:
            lines[i] = line.replace("bypass_req.data,", "data=bypass_req.data,")
            print(f"  [[符号][符号]] [符号][符号][符号]{i+1}[符号]: bypass_req.data [符号][符号]")
            modified = True
        
        # [符号][符号] strategy [符号][符号]
        if line.strip() == "strategy" and i > 0:
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] strategy=
            prev_line = lines[i-1] if i > 0 else ""
            if "strategy=" not in prev_line:
                lines[i] = line.replace("strategy", "strategy=strategy")
                print(f"  [[符号][符号]] [符号][符号][符号]{i+1}[符号]: strategy [符号][符号]")
                modified = True
    
    if modified:
        content = '\n'.join(lines)
    
    # [符号][符号]3: [符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
    replacements = [
        ("result.success", "result['success']"),
        ("result.risk_level.value", "result['risk_level']"),
        ("result.confidence", "result['confidence']"),
        ("result.fingerprint_quality", "result['fingerprint_quality']"),
        ("result.bypass_techniques", "result['bypass_techniques']"),
        ("result.warnings", "result['warnings']"),
        ("result.execution_time", "result['execution_time']"),
        ("result.timestamp.isoformat()", "result['timestamp']"),
    ]
    
    for old_pattern, new_pattern in replacements:
        if old_pattern in content and new_pattern not in content:
            content = content.replace(old_pattern, new_pattern)
            print(f"  [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]: {old_pattern} -> {new_pattern}")
    
    if content != original_content:
        return write_file(filepath, content)
    else:
        print("  [[符号][符号]] [符号][符号][符号][符号]")
        return True

def fix_device_fingerprint():
    """[符号][符号] device_fingerprint_engine.py"""
    filepath = 'src/core/device_fingerprint_engine.py'
    print(f"[[符号][符号]] [符号][符号] {filepath}...")
    
    content = read_file(filepath)
    if content is None:
        return False
    
    original_content = content
    
    # [符号][符号]1: [符号][符号] success_rate [符号][符号]
    if 'success_rate: float = 1.0' not in content:
        target_line = 'additional_headers: Optional[Dict[str, str]] = None  # [符号][符号]additional_headers[符号][符号][符号][符号][符号][符号][符号]'
        if target_line in content:
            content = content.replace(
                target_line,
                target_line + '\n    success_rate: float = 1.0  # [符号][符号][符号][符号][符号][符号][符号]'
            )
            print("  [[符号][符号]] [符号][符号] success_rate [符号][符号]")
    
    # [符号][符号]2: [符号][符号][符号][符号]
    if 'def is_available(self) -> bool:' not in content:
        methods_code = '''
    
    def is_available(self) -> bool:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        if not self.is_active:
            return False
        
        # [符号][符号][符号][符号][符号][符号]
        if self.last_used:
            from datetime import timedelta
            cooldown_minutes = 5  # [符号][符号][符号][符号][符号][符号]5[符号][符号]
            cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
            if datetime.now() < cooldown_end:
                return False
        
        return True
    
    def get_cooldown_remaining(self) -> float:
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        if not self.last_used:
            return 0.0
        
        from datetime import timedelta
        cooldown_minutes = 5  # [符号][符号][符号][符号][符号][符号]5[符号][符号]
        cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
        now = datetime.now()
        
        if now >= cooldown_end:
            return 0.0
        
        remaining = cooldown_end - now
        return remaining.total_seconds() / 60.0  # [符号][符号][符号][符号][符号]'''
        
        # [符号] success_rate [符号][符号][符号][符号][符号][符号][符号]
        target_line = 'success_rate: float = 1.0  # [符号][符号][符号][符号][符号][符号][符号]'
        if target_line in content:
            content = content.replace(target_line, target_line + methods_code)
            print("  [[符号][符号]] [符号][符号] is_available [符号] get_cooldown_remaining [符号][符号]")
    
    if content != original_content:
        return write_file(filepath, content)
    else:
        print("  [[符号][符号]] [符号][符号][符号][符号]")
        return True

def verify_syntax():
    """[符号][符号][符号][符号]"""
    print("[[符号][符号]] [符号][符号][符号][符号]...")
    
    files_to_check = [
        'src/core/bypass_engine.py',
        'src/core/api_service.py',
        'src/core/device_fingerprint_engine.py'
    ]
    
    all_ok = True
    for filepath in files_to_check:
        result = os.system(f'python3 -m py_compile {filepath}')
        if result == 0:
            print(f"  [[符号][符号]] {filepath} [符号][符号][符号][符号]")
        else:
            print(f"  [[符号][符号]] {filepath} [符号][符号][符号][符号]")
            all_ok = False
    
    return all_ok

def main():
    """[符号][符号][符号]"""
    print("[[符号][符号]] [符号][符号][符号][符号][符号][符号]...")
    
    success = True
    
    # [符号][符号][符号][符号]
    if not fix_bypass_engine():
        success = False
    
    if not fix_api_service():
        success = False
    
    if not fix_device_fingerprint():
        success = False
    
    # [符号][符号][符号][符号]
    if success and verify_syntax():
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        return True
    else:
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
PYTHON_SCRIPT_END

# 4. [符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]..."
python3 complete_fix.py

# [符号][符号][符号][符号][符号][符号]
if [ $? -eq 0 ]; then
    echo "[[符号][符号]] [符号][符号][符号][符号]"
else
    echo "[[符号][符号]] [符号][符号][符号][符号]"
    exit 1
fi

# 5. [符号][符号][符号][符号][符号][符号]
rm -f complete_fix.py safe_repair.py

# 6. [符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号]..."
sudo supervisorctl start starbucks_bypass

# 7. [符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]..."
sleep 15

# 8. [符号][符号][符号][符号][符号][符号]
echo "[[符号][符号]] [符号][符号][符号][符号][符号][符号]..."
sudo supervisorctl status starbucks_bypass

# 9. [符号][符号]API
echo "[[符号][符号]] [符号][符号]API..."
echo "[符号][符号][符号][符号]:"
curl -s http://localhost:8000/health

echo -e "\n[符号][符号][符号][符号]:"
curl -s http://localhost:8000/stats | jq '.' || curl -s http://localhost:8000/stats

echo -e "\n[符号][符号][符号][符号]:"
curl -s http://localhost:8000/devices | jq '.' || curl -s http://localhost:8000/devices

echo -e "\n[符号][符号][符号][符号][符号][符号]:"
curl -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' | jq '.' || echo "[符号][符号][符号][符号][符号][符号][符号][符号]"

echo -e "\n[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]"
