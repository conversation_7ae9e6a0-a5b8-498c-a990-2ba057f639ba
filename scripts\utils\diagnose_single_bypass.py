#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
单次绕过API诊断脚本
用于诊断单次绕过API失败的具体原因
"""

import sys
import os
import asyncio
import json
import traceback
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent / "starbucks_bypass_tester"
sys.path.insert(0, str(project_root / "src"))

async def diagnose_single_bypass():
    """诊断单次绕过功能"""
    print("🔍 开始诊断单次绕过API...")
    
    try:
        # 导入必要的模块
        from core.bypass_engine import BypassEngine, BypassStrategy, RiskLevel
        from core.device_fingerprint_engine import DeviceFingerprintEngine, DeviceFingerprint
        from core.anti_detection_engine import AntiDetectionEngine
        from config.config_manager import ConfigManager
        from utils.logger import get_logger
        
        print("✅ 模块导入成功")
        
        # 1. 初始化配置管理器
        config_manager = ConfigManager()
        print("✅ 配置管理器初始化成功")
        
        # 2. 初始化日志
        logger = get_logger("diagnose")
        print("✅ 日志初始化成功")
        
        # 3. 初始化设备指纹引擎
        fingerprint_engine = DeviceFingerprintEngine(config_manager)
        await fingerprint_engine.initialize()
        print(f"✅ 设备指纹引擎初始化成功，设备数量: {len(fingerprint_engine.fingerprint_pool)}")
        
        # 4. 初始化反检测引擎
        anti_detection_engine = AntiDetectionEngine(config_manager)
        print("✅ 反检测引擎初始化成功")
        
        # 5. 初始化绕过引擎
        bypass_engine = BypassEngine(config_manager, anti_detection_engine, fingerprint_engine)
        await bypass_engine.initialize()
        print("✅ 绕过引擎初始化成功")
        
        # 6. 测试单次绕过
        print("\n🧪 测试单次绕过...")
        
        test_data = {
            "target_url": "https://httpbin.org/get",
            "method": "GET",
            "data": None,
            "strategy": BypassStrategy.ADAPTIVE
        }
        
        print(f"测试参数: {test_data}")
        
        result = await bypass_engine.execute_bypass(
            test_data["target_url"],
            test_data["method"],
            data=test_data["data"],
            strategy=test_data["strategy"]
        )
        
        print(f"\n✅ 单次绕过测试成功!")
        print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False, default=str)}")
        
        # 7. 验证结果结构
        expected_keys = [
            'success', 'confidence', 'modified_fingerprint', 'strategy_used',
            'risk_level', 'fingerprint_quality', 'bypass_techniques', 
            'warnings', 'execution_time', 'timestamp'
        ]
        
        missing_keys = [key for key in expected_keys if key not in result]
        if missing_keys:
            print(f"⚠️ 缺失的键: {missing_keys}")
        else:
            print("✅ 结果结构完整")
        
        # 8. 检查风险等级
        if 'risk_level' in result:
            risk_level = result['risk_level']
            if isinstance(risk_level, str):
                print(f"✅ 风险等级格式正确: {risk_level}")
            else:
                print(f"⚠️ 风险等级格式异常: {type(risk_level)} - {risk_level}")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        print(f"错误详情:\n{traceback.format_exc()}")
        return False

async def test_api_compatibility():
    """测试API兼容性"""
    print("\n🔍 测试API兼容性...")
    
    try:
        # 模拟API请求数据结构
        from core.api_service import BypassRequest, BypassResponse
        
        # 创建测试请求
        request_data = {
            "target_url": "https://httpbin.org/get",
            "method": "GET",
            "strategy": "adaptive",
            "data": None
        }
        
        print(f"API请求数据: {request_data}")
        
        # 验证请求模型
        try:
            request = BypassRequest(**request_data)
            print("✅ 请求模型验证成功")
        except Exception as e:
            print(f"❌ 请求模型验证失败: {e}")
            return False
        
        # 模拟响应数据
        response_data = {
            "success": True,
            "request_id": "test_123",
            "risk_level": "low",
            "confidence": 0.8,
            "fingerprint_quality": 0.9,
            "bypass_techniques": ["header_randomization"],
            "warnings": [],
            "execution_time": 0.5,
            "timestamp": "2025-07-31T15:30:00"
        }
        
        # 验证响应模型
        try:
            response = BypassResponse(**response_data)
            print("✅ 响应模型验证成功")
        except Exception as e:
            print(f"❌ 响应模型验证失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API兼容性测试失败: {e}")
        print(f"错误详情:\n{traceback.format_exc()}")
        return False

def check_imports():
    """检查导入"""
    print("🔍 检查模块导入...")
    
    try:
        # 检查核心模块
        modules_to_check = [
            "core.bypass_engine",
            "core.device_fingerprint_engine", 
            "core.anti_detection_engine",
            "core.api_service",
            "config.config_manager",
            "utils.logger"
        ]
        
        for module_name in modules_to_check:
            try:
                __import__(module_name)
                print(f"✅ {module_name}")
            except ImportError as e:
                print(f"❌ {module_name}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入检查失败: {e}")
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("单次绕过API诊断工具")
    print("=" * 60)
    
    # 1. 检查导入
    if not check_imports():
        print("❌ 模块导入检查失败")
        return False
    
    # 2. 测试API兼容性
    if not await test_api_compatibility():
        print("❌ API兼容性测试失败")
        return False
    
    # 3. 诊断单次绕过
    if not await diagnose_single_bypass():
        print("❌ 单次绕过诊断失败")
        return False
    
    print("\n🎉 所有诊断测试通过!")
    return True

if __name__ == "__main__":
    # 切换到项目目录
    os.chdir(project_root)
    
    # 运行诊断
    success = asyncio.run(main())
    
    if success:
        print("\n✅ 诊断完成 - 单次绕过功能正常")
        sys.exit(0)
    else:
        print("\n❌ 诊断失败 - 发现问题")
        sys.exit(1)
