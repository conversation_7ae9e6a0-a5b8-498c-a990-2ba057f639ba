#!/bin/bash

# 修复服务器上的API服务文件
echo "🔧 修复服务器上的API服务文件..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 4. 备份当前文件
echo "📦 备份当前API服务文件..."
cp src/core/api_service.py src/core/api_service.py.broken_backup.$(date +%Y%m%d_%H%M%S)

# 5. 检查并修复API服务文件
echo "🔧 检查并修复API服务文件..."
python3 << 'FIX_API_SERVICE'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

# 读取当前文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

print('🔧 分析API服务文件问题...')

# 检查是否有孤立的@app.on_event装饰器
lines = content.split('\n')
problematic_lines = []
for i, line in enumerate(lines):
    if '@app.on_event(' in line and 'api_service.app.on_event(' not in line:
        problematic_lines.append(i + 1)
        print(f'❌ 发现问题行 {i + 1}: {line.strip()}')

if problematic_lines:
    print('🔧 修复孤立的@app.on_event装饰器...')
    
    # 移除所有孤立的@app.on_event及其相关代码
    new_lines = []
    skip_until_next_def = False
    
    for i, line in enumerate(lines):
        # 如果遇到孤立的@app.on_event，开始跳过
        if '@app.on_event(' in line and 'api_service.app.on_event(' not in line:
            skip_until_next_def = True
            print(f'  跳过问题行 {i + 1}: {line.strip()}')
            continue
        
        # 如果在跳过模式中，继续跳过直到遇到下一个函数定义或类定义
        if skip_until_next_def:
            if (line.strip().startswith('def ') or 
                line.strip().startswith('class ') or 
                line.strip().startswith('if __name__') or
                line.strip().startswith('# ') and len(line.strip()) > 10):
                skip_until_next_def = False
                new_lines.append(line)
            else:
                print(f'  跳过相关行 {i + 1}: {line.strip()}')
                continue
        else:
            new_lines.append(line)
    
    content = '\n'.join(new_lines)
    print('✅ 孤立的@app.on_event装饰器已移除')

# 检查是否有重复的startup事件定义
startup_events = content.count('@api_service.app.on_event("startup")')
if startup_events > 1:
    print(f'❌ 发现重复的startup事件: {startup_events}个')
    
    # 保留第一个，移除其他的
    lines = content.split('\n')
    new_lines = []
    startup_found = False
    skip_startup = False
    
    for i, line in enumerate(lines):
        if '@api_service.app.on_event("startup")' in line:
            if not startup_found:
                startup_found = True
                new_lines.append(line)
            else:
                skip_startup = True
                print(f'  移除重复的startup事件 {i + 1}')
                continue
        elif skip_startup:
            if (line.strip().startswith('def ') or 
                line.strip().startswith('class ') or 
                line.strip().startswith('if __name__')):
                skip_startup = False
                new_lines.append(line)
            else:
                continue
        else:
            new_lines.append(line)
    
    content = '\n'.join(new_lines)
    print('✅ 重复的startup事件已移除')

# 确保文件末尾有正确的app导出
if 'app = get_app()' not in content:
    print('🔧 添加app导出...')
    content += '\n\n# 导出app供uvicorn使用\napp = get_app()\n'
    print('✅ app导出已添加')

# 写入修复后的文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ API服务文件修复完成')
FIX_API_SERVICE

if [ $? -ne 0 ]; then
    echo "❌ API服务文件修复失败"
    deactivate
    exit 1
fi

# 6. 验证语法
echo "🔍 验证语法..."
cd src
python3 -c "
import ast
with open('core/api_service.py', 'r') as f:
    content = f.read()
try:
    ast.parse(content)
    print('✅ 语法验证通过')
except SyntaxError as e:
    print(f'❌ 语法错误: 第{e.lineno}行: {e.text.strip() if e.text else \"未知\"}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 语法验证失败"
    deactivate
    exit 1
fi

# 7. 测试模块导入
echo "🔍 测试模块导入..."
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    from core.api_service import app
    print('✅ API服务模块导入成功')
except Exception as e:
    print(f'❌ API服务模块导入失败: {e}')
    exit(1)

try:
    from core.bypass_engine import BypassEngine
    print('✅ 绕过引擎模块导入成功')
except Exception as e:
    print(f'❌ 绕过引擎模块导入失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 模块导入测试失败"
    deactivate
    exit 1
fi

# 8. 启动服务
echo "🚀 启动服务..."
cd /home/<USER>/apps/starbucks_bypass_tester
sudo supervisorctl start starbucks_bypass

# 9. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 10. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 11. 检查启动日志
echo "📋 检查启动日志..."
echo "=== 最近的输出日志 ==="
tail -15 logs/output.log 2>/dev/null || echo "无输出日志"
echo ""
echo "=== 最近的错误日志 ==="
tail -10 logs/error.log 2>/dev/null || echo "无错误日志"

# 12. 测试API接口
echo "🧪 测试API接口..."

# 健康检查
echo "测试健康检查:"
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
    echo "$health_response"
else
    echo "❌ 健康检查失败: $health_response"
fi

# 单次绕过
echo ""
echo "测试单次绕过:"
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
fi

# 13. 总结
echo ""
echo "🎯 修复总结:"
if echo "$health_response" | grep -q '"status"' && echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ API服务修复成功！"
    echo "🎉 现在可以运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo "❌ API服务仍有问题，需要进一步调试"
    echo "查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
