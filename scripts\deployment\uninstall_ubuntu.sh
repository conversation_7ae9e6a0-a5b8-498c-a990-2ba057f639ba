#!/bin/bash

# 星巴克设备指纹绕过系统 - Ubuntu环境卸载脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否为root用户 - 禁止root用户直接执行
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "安全限制: 禁止使用root用户直接执行卸载脚本"
        echo ""
        echo "原因:"
        echo "  - 避免意外删除系统关键文件"
        echo "  - 防止误操作导致系统损坏"
        echo "  - 确保安全的卸载流程"
        echo ""
        echo "正确的执行方式:"
        echo "  1. 使用普通用户登录系统"
        echo "  2. 确保该用户具有sudo权限"
        echo "  3. 运行: ./scripts/uninstall_ubuntu.sh"
        echo ""
        echo "脚本将安全地:"
        echo "  - 停止所有相关服务"
        echo "  - 删除应用和配置"
        echo "  - 删除专用用户: starbucks"
        echo ""
        exit 1
    fi
}

# 停止所有相关服务
stop_services() {
    log_info "停止星巴克绕过系统服务..."

    # 停止应用服务
    if [[ $EUID -eq 0 ]]; then
        if supervisorctl status starbucks_bypass >/dev/null 2>&1; then
            supervisorctl stop starbucks_bypass
            log_success "已停止应用服务"
        else
            log_warning "应用服务未运行或不存在"
        fi
    else
        if sudo supervisorctl status starbucks_bypass >/dev/null 2>&1; then
            sudo supervisorctl stop starbucks_bypass
            log_success "已停止应用服务"
        else
            log_warning "应用服务未运行或不存在"
        fi
    fi
    
    # 停止Nginx (如果只用于此应用)
    if systemctl is-active --quiet nginx; then
        read -p "是否停止Nginx服务? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo systemctl stop nginx
            log_success "已停止Nginx服务"
        fi
    fi
    
    # 停止Supervisor (如果只用于此应用)
    if systemctl is-active --quiet supervisor; then
        read -p "是否停止Supervisor服务? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo systemctl stop supervisor
            log_success "已停止Supervisor服务"
        fi
    fi
}

# 删除应用文件
remove_application() {
    log_info "删除应用文件..."
    
    # 删除应用目录
    if [ -d "/home/<USER>/apps/starbucks_bypass_tester" ]; then
        sudo rm -rf /home/<USER>/apps/starbucks_bypass_tester
        log_success "已删除应用目录"
    else
        log_warning "应用目录不存在"
    fi
    
    # 删除日志目录
    if [ -d "/home/<USER>/logs" ]; then
        sudo rm -rf /home/<USER>/logs
        log_success "已删除日志目录"
    fi
    
    # 删除数据目录
    if [ -d "/home/<USER>/data" ]; then
        read -p "是否删除数据目录? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo rm -rf /home/<USER>/data
            log_success "已删除数据目录"
        fi
    fi
}

# 删除配置文件
remove_configurations() {
    log_info "删除配置文件..."
    
    # 删除Supervisor配置
    if [ -f "/etc/supervisor/conf.d/starbucks_bypass.conf" ]; then
        sudo rm -f /etc/supervisor/conf.d/starbucks_bypass.conf
        log_success "已删除Supervisor配置"
    fi
    
    # 删除Nginx配置
    if [ -f "/etc/nginx/sites-available/starbucks_bypass" ]; then
        sudo rm -f /etc/nginx/sites-available/starbucks_bypass
        log_success "已删除Nginx配置文件"
    fi
    
    if [ -L "/etc/nginx/sites-enabled/starbucks_bypass" ]; then
        sudo rm -f /etc/nginx/sites-enabled/starbucks_bypass
        log_success "已删除Nginx配置链接"
    fi
    
    # 重新加载配置
    if systemctl is-active --quiet supervisor; then
        sudo supervisorctl reread >/dev/null 2>&1 || true
        sudo supervisorctl update >/dev/null 2>&1 || true
        log_success "已重新加载Supervisor配置"
    fi
    
    if systemctl is-active --quiet nginx; then
        if sudo nginx -t >/dev/null 2>&1; then
            sudo systemctl reload nginx
            log_success "已重新加载Nginx配置"
        else
            log_warning "Nginx配置测试失败，请手动检查"
        fi
    fi
}

# 删除防火墙规则
remove_firewall_rules() {
    log_info "删除应用相关防火墙规则..."

    # 检查UFW是否启用
    if sudo ufw status | grep -q "Status: active"; then
        # 删除应用端口规则
        sudo ufw delete allow 8000 >/dev/null 2>&1 || true
        log_success "已删除应用端口8000规则"

        # 删除Nginx规则
        sudo ufw delete allow 8094 >/dev/null 2>&1 || true
        log_success "已删除Nginx端口8094规则"

        # 保留SSH端口规则
        log_info "保留SSH端口规则 (22, 28262)"

        # 显示当前规则
        log_info "当前防火墙规则:"
        sudo ufw status numbered

        # 验证SSH端口仍然开放
        if sudo ufw status | grep -q "28262"; then
            log_success "[成功] SSH端口28262规则已保留"
        else
            log_warning "[警告] SSH端口28262规则未找到"
        fi
    else
        log_warning "UFW防火墙未启用"
    fi
}

# 提醒用户删除操作
remind_user_deletion() {
    local APP_USER="starbucks"

    log_info "用户管理提醒"

    # 检查用户是否存在
    if id "$APP_USER" >/dev/null 2>&1; then
        echo ""
        log_warning "检测到用户 $APP_USER 仍然存在"
        echo ""
        echo "用户信息:"
        id "$APP_USER"
        echo ""

        echo "📋 用户删除说明:"
        echo "  - 应用已卸载，但用户 $APP_USER 仍然存在"
        echo "  - 如需完全删除用户，请使用专用删除脚本"
        echo "  - 用户删除是可选操作，可以保留用户供将来使用"
        echo ""

        echo "🗑️ 完全删除用户 (可选):"
        echo "  如需删除用户 $APP_USER 和所有相关数据，请运行:"
        echo "  sudo ./scripts/delete_user.sh"
        echo ""
        echo "[警告]  警告: 删除用户将永久删除所有用户数据！"
        echo ""

        echo "🔄 保留用户 (推荐):"
        echo "  - 保留用户可以方便将来重新安装"
        echo "  - 用户的SSH配置和权限将被保留"
        echo "  - 只需重新运行安装脚本即可恢复应用"
        echo ""

        log_info "用户 $APP_USER 已保留"
        echo ""
        echo "💡 提示:"
        echo "  - 如需删除用户，请单独运行: sudo ./scripts/delete_user.sh"
        echo "  - 用户已保留，可以用于将来重新安装"
        echo ""
    else
        log_info "用户 $APP_USER 不存在"
    fi
}

# 卸载软件包
remove_packages() {
    read -p "是否卸载相关软件包? (supervisor, nginx等) (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "卸载软件包..."
        
        # 卸载应用相关包
        sudo apt remove -y supervisor nginx >/dev/null 2>&1 || true
        log_success "已卸载supervisor和nginx"
        
        # 询问是否卸载Python开发环境
        read -p "是否卸载Python开发环境? (python3-pip, python3-venv等) (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo apt remove -y python3-pip python3-venv python3-dev >/dev/null 2>&1 || true
            log_success "已卸载Python开发环境"
        fi
        
        # 询问是否卸载编译工具
        read -p "是否卸载编译工具? (build-essential等) (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo apt remove -y build-essential libssl-dev libffi-dev >/dev/null 2>&1 || true
            log_success "已卸载编译工具"
        fi
        
        # 清理包缓存
        log_info "清理包缓存..."
        sudo apt autoremove -y >/dev/null 2>&1 || true
        sudo apt autoclean >/dev/null 2>&1 || true
        log_success "已清理包缓存"
    else
        log_info "保留已安装的软件包"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 清理Python缓存
    find /home/<USER>"__pycache__" -type d -exec sudo rm -rf {} + 2>/dev/null || true
    find /home/<USER>"*.pyc" -type f -exec sudo rm -f {} + 2>/dev/null || true
    
    # 清理日志文件
    sudo find /var/log -name "*starbucks*" -type f -exec rm -f {} + 2>/dev/null || true
    
    # 清理临时文件
    sudo rm -f /tmp/*starbucks* 2>/dev/null || true
    
    log_success "已清理临时文件"
}

# 验证卸载结果
verify_uninstall() {
    log_info "验证卸载结果..."
    
    local issues=0
    
    # 检查应用目录
    if [ -d "/home/<USER>/apps/starbucks_bypass_tester" ]; then
        log_warning "应用目录仍然存在"
        ((issues++))
    fi
    
    # 检查配置文件
    if [ -f "/etc/supervisor/conf.d/starbucks_bypass.conf" ]; then
        log_warning "Supervisor配置文件仍然存在"
        ((issues++))
    fi
    
    if [ -f "/etc/nginx/sites-available/starbucks_bypass" ]; then
        log_warning "Nginx配置文件仍然存在"
        ((issues++))
    fi
    
    # 检查进程
    if pgrep -f "starbucks_bypass" >/dev/null; then
        log_warning "相关进程仍在运行"
        ((issues++))
    fi
    
    if [ $issues -eq 0 ]; then
        log_success "卸载验证通过，所有组件已成功移除"
    else
        log_warning "发现 $issues 个潜在问题，请手动检查"
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "  星巴克设备指纹绕过系统 - 卸载工具"
    echo "=========================================="
    echo
    
    # 检查权限
    check_root

    # 安全警告
    echo ""
    log_error "[警告]  危险操作警告 [警告]"
    echo ""
    echo "此操作将完全卸载星巴克设备指纹绕过系统，包括:"
    echo ""
    echo "🔴 将要删除的内容:"
    echo "  - 停止所有相关服务"
    echo "  - 删除应用程序和数据"
    echo "  - 删除系统配置文件"
    echo "  - 删除防火墙规则"
    echo "  - 卸载相关软件包 (可选)"
    echo ""
    echo "🔵 不会删除的内容:"
    echo "  - 用户账户 (需要单独删除)"
    echo "  - 用户SSH配置"
    echo "  - 用户个人文件"
    echo ""
    echo "[警告]  注意事项:"
    echo "  - 应用数据将被永久删除"
    echo "  - 系统配置文件将被移除"
    echo "  - 用户账户将被保留 (可选择删除)"
    echo "  - 此操作不可恢复"
    echo ""

    # 确认卸载
    read -p "确认继续卸载? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消卸载操作"
        exit 0
    fi

    # 二次确认
    echo ""
    read -p "最终确认: 输入 'UNINSTALL' 继续卸载: " confirm
    if [[ "$confirm" != "UNINSTALL" ]]; then
        log_info "确认失败，取消卸载操作"
        exit 0
    fi
    
    echo
    log_info "开始卸载过程..."
    echo
    
    # 执行卸载步骤
    stop_services
    echo
    
    remove_application
    echo
    
    remove_configurations
    echo
    
    remove_firewall_rules
    echo

    remind_user_deletion
    echo

    remove_packages
    echo
    
    cleanup_temp_files
    echo
    
    verify_uninstall
    echo
    
    log_success "卸载完成！"
    echo
}

# 执行主函数
main "$@"
