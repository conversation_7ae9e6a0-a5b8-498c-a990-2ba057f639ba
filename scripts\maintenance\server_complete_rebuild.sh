#!/bin/bash

# 完全重建API服务文件 - 解决转义字符损坏问题
echo "🔧 完全重建API服务文件..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 备份当前文件
echo "📦 备份当前文件..."
cp src/core/api_service.py src/core/api_service.py.damaged.$(date +%Y%m%d_%H%M%S)

# 3. 从最早的备份恢复（如果存在）
echo "🔄 尝试从备份恢复..."
EARLIEST_BACKUP=$(ls -tr src/core/api_service.py.backup.* 2>/dev/null | head -1)
if [ -n "$EARLIEST_BACKUP" ]; then
    echo "找到最早备份: $EARLIEST_BACKUP"
    cp "$EARLIEST_BACKUP" src/core/api_service.py
    echo "✅ 已恢复到最早备份"
else
    echo "⚠️ 未找到备份文件"
fi

# 4. 清理所有转义字符错误
echo "🧹 清理转义字符错误..."
python3 << 'CLEANUP_SCRIPT'
import re

# 读取文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

print('🧹 清理转义字符错误...')

# 清理所有可能的转义字符错误
content = content.replace("\\'", "'")
content = content.replace("\\\"", '"')

# 清理重复的字段定义
lines = content.split('\n')
cleaned_lines = []
seen_strategy_used = False
seen_modified_fingerprint = False

for line in lines:
    # 避免重复添加字段
    if 'strategy_used: Optional[str] = None' in line:
        if not seen_strategy_used:
            cleaned_lines.append(line)
            seen_strategy_used = True
    elif 'modified_fingerprint: Optional[Dict[str, Any]] = None' in line:
        if not seen_modified_fingerprint:
            cleaned_lines.append(line)
            seen_modified_fingerprint = True
    else:
        cleaned_lines.append(line)

content = '\n'.join(cleaned_lines)

# 写入清理后的文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ 转义字符清理完成')
CLEANUP_SCRIPT

# 5. 应用正确的修复
echo "🔧 应用正确的修复..."
python3 << 'FIX_SCRIPT'
import re

# 读取文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

print('🔧 修复 BypassResponse 模型...')

# 1. 确保BypassResponse模型包含新字段
if 'strategy_used: Optional[str] = None' not in content:
    # 查找BypassResponse类定义
    pattern = r'(class BypassResponse\(BaseModel\):\s*"""绕过响应模型"""\s*success: bool\s*request_id: str\s*risk_level: str\s*confidence: float\s*fingerprint_quality: float\s*bypass_techniques: List\[str\]\s*warnings: List\[str\]\s*execution_time: float\s*timestamp: str\s*)(response_data: Optional\[Dict\[str, Any\]\] = None)'
    
    replacement = r'\1strategy_used: Optional[str] = None\n    modified_fingerprint: Optional[Dict[str, Any]] = None\n    \2'
    
    if re.search(pattern, content, re.DOTALL):
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        print('  ✅ BypassResponse 模型字段已添加')
    else:
        # 备用方法
        old_text = 'timestamp: str\n    response_data: Optional[Dict[str, Any]] = None'
        new_text = 'timestamp: str\n    strategy_used: Optional[str] = None\n    modified_fingerprint: Optional[Dict[str, Any]] = None\n    response_data: Optional[Dict[str, Any]] = None'
        if old_text in content:
            content = content.replace(old_text, new_text)
            print('  ✅ BypassResponse 模型字段已添加 (备用方法)')
else:
    print('  ℹ️ BypassResponse 模型已包含新字段')

print('🔧 修复响应构建...')

# 2. 修复单次绕过响应构建
single_pattern = r'(response = BypassResponse\(\s*success=result\[\'success\'\],\s*request_id=request_id,\s*risk_level=result\[\'risk_level\'\],\s*confidence=result\[\'confidence\'\],\s*fingerprint_quality=result\[\'fingerprint_quality\'\],\s*bypass_techniques=result\[\'bypass_techniques\'\],\s*warnings=result\[\'warnings\'\],\s*execution_time=result\[\'execution_time\'\],\s*timestamp=result\[\'timestamp\'\]\s*)\)'

single_replacement = r'\1,\n                    strategy_used=result.get(\'strategy_used\'),\n                    modified_fingerprint=None  # 不返回敏感的指纹信息\n                )'

if re.search(single_pattern, content, re.DOTALL):
    if 'strategy_used=result.get(' not in content:
        content = re.sub(single_pattern, single_replacement, content, flags=re.DOTALL)
        print('  ✅ 单次绕过响应构建已修复')
    else:
        print('  ℹ️ 单次绕过响应构建已修复')
else:
    print('  ⚠️ 未找到单次绕过响应构建模式')

# 3. 修复批量绕过响应构建
batch_pattern = r'(return BypassResponse\(\s*success=result\[\'success\'\],\s*request_id=request_id,\s*risk_level=result\[\'risk_level\'\],\s*confidence=result\[\'confidence\'\],\s*fingerprint_quality=result\[\'fingerprint_quality\'\],\s*bypass_techniques=result\[\'bypass_techniques\'\],\s*warnings=result\[\'warnings\'\],\s*execution_time=result\[\'execution_time\'\],\s*timestamp=result\[\'timestamp\'\]\s*)\)'

batch_replacement = r'\1,\n                                strategy_used=result.get(\'strategy_used\'),\n                                modified_fingerprint=None\n                            )'

if re.search(batch_pattern, content, re.DOTALL):
    content = re.sub(batch_pattern, batch_replacement, content, flags=re.DOTALL)
    print('  ✅ 批量绕过响应构建已修复')

# 4. 修复错误响应构建
error_pattern = r'(return BypassResponse\(\s*success=False,\s*request_id=request_id,\s*risk_level=RiskLevel\.CRITICAL\.value,\s*confidence=0\.0,\s*fingerprint_quality=0\.0,\s*bypass_techniques=\[\],\s*warnings=\[f"执行错误: \{str\(e\)\}"\],\s*execution_time=0\.0,\s*timestamp=datetime\.now\(\)\.isoformat\(\)\s*)\)'

error_replacement = r'\1,\n                                strategy_used=None,\n                                modified_fingerprint=None\n                            )'

if re.search(error_pattern, content, re.DOTALL):
    content = re.sub(error_pattern, error_replacement, content, flags=re.DOTALL)
    print('  ✅ 错误响应构建已修复')

# 写入文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ API修复完成')
FIX_SCRIPT

# 6. 验证语法
echo "🔍 验证语法..."
python3 -m py_compile src/core/api_service.py
if [ $? -eq 0 ]; then
    echo "✅ 语法验证通过"
else
    echo "❌ 语法验证失败"
    echo "显示错误行附近的内容："
    python3 -c "
import sys
try:
    compile(open('src/core/api_service.py').read(), 'src/core/api_service.py', 'exec')
except SyntaxError as e:
    print(f'语法错误在第 {e.lineno} 行: {e.text}')
    with open('src/core/api_service.py') as f:
        lines = f.readlines()
        start = max(0, e.lineno - 3)
        end = min(len(lines), e.lineno + 2)
        for i in range(start, end):
            marker = '>>> ' if i == e.lineno - 1 else '    '
            print(f'{marker}{i+1:3d}: {lines[i].rstrip()}')
"
    exit 1
fi

# 7. 重启服务
echo "🚀 重启服务..."
sudo supervisorctl start starbucks_bypass

# 8. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 9. 测试API
echo "🧪 测试单次绕过API..."
response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "$response" | head -3
else
    echo "❌ 单次绕过API测试失败"
    echo "$response"
fi

echo ""
echo "🎉 完全重建完成！现在运行完整测试："
echo "./scripts/run_all_tests.sh"
