#!/bin/bash

# 星巴克设备指纹绕过系统 - 实时监控脚本
# 用途: 实时监控系统运行状态和性能指标

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 监控间隔（秒）
INTERVAL=2

# 清屏函数
clear_screen() {
    clear
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  星巴克设备指纹绕过系统 - 实时监控${NC}"
    echo -e "${BLUE}  按 Ctrl+C 退出监控${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
}

# 获取当前时间
get_timestamp() {
    date '+%H:%M:%S'
}

# 获取API统计信息
get_api_stats() {
    local stats_response
    stats_response=$(curl -s http://localhost:8000/stats 2>/dev/null || echo '{}')
    
    if command -v jq > /dev/null 2>&1; then
        local total_requests=$(echo "$stats_response" | jq -r '.total_requests // 0')
        local successful_requests=$(echo "$stats_response" | jq -r '.successful_requests // 0')
        local failed_requests=$(echo "$stats_response" | jq -r '.failed_requests // 0')
        local success_rate=$(echo "$stats_response" | jq -r '.success_rate // 0')
        local avg_response_time=$(echo "$stats_response" | jq -r '.avg_response_time // 0')
        
        echo "总请求:$total_requests 成功:$successful_requests 失败:$failed_requests 成功率:${success_rate}% 平均响应:${avg_response_time}s"
    else
        echo "API统计: 需要jq工具解析"
    fi
}

# 获取设备使用情况
get_device_usage() {
    local devices_response
    devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null || echo '[]')
    
    if command -v jq > /dev/null 2>&1; then
        local active_devices=$(echo "$devices_response" | jq -r '[.[] | select(.use_count > 0)] | length')
        local total_devices=$(echo "$devices_response" | jq -r '. | length')
        local healthy_devices=$(echo "$devices_response" | jq -r '[.[] | select(.is_healthy == true)] | length')
        
        echo "设备池: ${active_devices}/${total_devices} 活跃, ${healthy_devices} 健康"
        
        # 显示使用最多的前3个设备
        local top_devices=$(echo "$devices_response" | jq -r 'sort_by(-.use_count) | .[0:3] | .[] | "\(.device_id[0:12])(\(.use_count))"' | tr '\n' ' ')
        if [ -n "$top_devices" ]; then
            echo "热门设备: $top_devices"
        fi
    else
        echo "设备状态: 需要jq工具解析"
    fi
}

# 获取系统资源使用情况
get_system_resources() {
    # CPU使用率
    local cpu_usage="未知"
    if command -v top > /dev/null 2>&1; then
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}' 2>/dev/null || echo "未知")
    fi
    
    # 内存使用率
    local mem_usage="未知"
    local mem_total="未知"
    local mem_used="未知"
    if command -v free > /dev/null 2>&1; then
        mem_info=$(free -h | grep Mem)
        mem_total=$(echo "$mem_info" | awk '{print $2}')
        mem_used=$(echo "$mem_info" | awk '{print $3}')
        mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    fi
    
    echo "系统资源: CPU ${cpu_usage}%, 内存 ${mem_used}/${mem_total} (${mem_usage}%)"
}

# 获取网络连接数
get_network_connections() {
    local connections=0
    if command -v netstat > /dev/null 2>&1; then
        connections=$(netstat -an | grep ":8000 " | grep ESTABLISHED | wc -l)
    fi
    echo "活跃连接: $connections"
}

# 获取最近的日志条目
get_recent_logs() {
    if [ -f "logs/application.log" ]; then
        local recent_errors=$(tail -50 logs/application.log | grep -i error | wc -l)
        local recent_warnings=$(tail -50 logs/application.log | grep -i warning | wc -l)
        echo "最近日志: ${recent_errors} 错误, ${recent_warnings} 警告"
        
        # 显示最新的一条重要日志
        local latest_important=$(tail -20 logs/application.log | grep -E "(ERROR|WARNING|SUCCESS)" | tail -1)
        if [ -n "$latest_important" ]; then
            local log_time=$(echo "$latest_important" | awk '{print $1, $2}')
            local log_level=$(echo "$latest_important" | grep -o -E "(ERROR|WARNING|SUCCESS)")
            echo "最新事件: [$log_time] $log_level"
        fi
    else
        echo "日志文件: 不存在"
    fi
}

# 检查API服务状态
check_api_health() {
    if curl -s --connect-timeout 2 http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "${GREEN}API服务: 运行中 ✓${NC}"
        return 0
    else
        echo -e "${RED}API服务: 离线 ✗${NC}"
        return 1
    fi
}

# 主监控循环
main_monitor() {
    local counter=0
    
    while true; do
        clear_screen
        
        local timestamp=$(get_timestamp)
        echo -e "${CYAN}[${timestamp}] 监控周期: $((counter + 1))${NC}"
        echo ""
        
        # API服务状态
        check_api_health
        
        # 如果API服务正常，获取详细统计
        if curl -s --connect-timeout 2 http://localhost:8000/health > /dev/null 2>&1; then
            echo ""
            echo -e "${YELLOW}[统计] API统计信息${NC}"
            get_api_stats
            echo ""
            
            echo -e "${YELLOW}[工具] 设备使用情况${NC}"
            get_device_usage
            echo ""
        else
            echo -e "${RED}[警告]  API服务离线，无法获取详细统计${NC}"
            echo ""
        fi
        
        # 系统资源
        echo -e "${YELLOW}[电脑] 系统资源${NC}"
        get_system_resources
        get_network_connections
        echo ""
        
        # 日志信息
        echo -e "${YELLOW}[文档] 日志信息${NC}"
        get_recent_logs
        echo ""
        
        # 实时性能测试
        if [ $((counter % 10)) -eq 0 ] && curl -s --connect-timeout 2 http://localhost:8000/health > /dev/null 2>&1; then
            echo -e "${YELLOW}[启动] 性能测试${NC}"
            local test_start=$(date +%s.%N)
            local test_response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 5 \
                -X POST http://localhost:8000/bypass/single \
                -H "Content-Type: application/json" \
                -d '{"target_url": "https://httpbin.org/get", "method": "GET", "strategy": "adaptive"}' 2>/dev/null || echo "000")
            local test_end=$(date +%s.%N)
            local test_duration=$(echo "$test_end - $test_start" | bc -l 2>/dev/null || echo "0")
            
            if [ "$test_response" = "200" ]; then
                echo -e "测试请求: ${GREEN}成功${NC} (${test_duration}s)"
            else
                echo -e "测试请求: ${RED}失败 (HTTP $test_response)${NC}"
            fi
            echo ""
        fi
        
        # 底部状态栏
        echo -e "${BLUE}========================================${NC}"
        echo -e "${CYAN}监控间隔: ${INTERVAL}s | 下次更新: $((INTERVAL))s后 | 按 Ctrl+C 退出${NC}"
        
        # 等待下一个监控周期
        sleep $INTERVAL
        counter=$((counter + 1))
    done
}

# 信号处理
cleanup() {
    echo ""
    echo -e "${YELLOW}监控已停止${NC}"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 检查依赖
echo -e "${BLUE}正在启动监控...${NC}"

# 检查curl
if ! command -v curl > /dev/null 2>&1; then
    echo -e "${RED}错误: 需要安装curl工具${NC}"
    exit 1
fi

# 检查bc (用于浮点数计算)
if ! command -v bc > /dev/null 2>&1; then
    echo -e "${YELLOW}警告: 建议安装bc工具以获得更精确的计算${NC}"
fi

# 检查jq (用于JSON解析)
if ! command -v jq > /dev/null 2>&1; then
    echo -e "${YELLOW}警告: 建议安装jq工具以获得更详细的API统计${NC}"
fi

echo -e "${GREEN}监控启动成功！${NC}"
sleep 1

# 开始监控
main_monitor
