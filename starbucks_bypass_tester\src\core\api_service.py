#!/usr/bin/env python3
"""
API服务 - 星巴克设备指纹绕过系统RESTful API服务
API Service - RESTful API service for Starbucks device fingerprint bypass
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass, asdict
import logging

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

try:
    from ..utils.logger import get_logger
    from ..config.config_manager import ConfigManager
    from .device_fingerprint_engine import DeviceFingerprintEngine
    from .concurrency_controller import ConcurrencyController
    from .bypass_engine import BypassEngine, BypassStrategy, RiskLevel
except ImportError:
    # 用于直接运行测试
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from utils.logger import get_logger
    from config.config_manager import ConfigManager
    from core.device_fingerprint_engine import DeviceFingerprintEngine
    from core.concurrency_controller import Concurrency<PERSON>ontroller
    from core.bypass_engine import BypassEngine, BypassStrategy, RiskLevel


# Pydantic模型定义
class BypassRequest(BaseModel):
    """绕过请求模型"""
    target_url: str = Field(..., description="目标URL")
    method: str = Field(default="GET", description="HTTP方法")
    headers: Optional[Dict[str, str]] = Field(default=None, description="自定义请求头")
    data: Optional[Dict[str, Any]] = Field(default=None, description="请求数据")
    strategy: Optional[str] = Field(default="conservative", description="绕过策略")
    timeout: Optional[int] = Field(default=30, description="超时时间(秒)")


class BatchBypassRequest(BaseModel):
    """批量绕过请求模型"""
    requests: List[BypassRequest] = Field(..., description="请求列表")
    max_concurrent: int = Field(default=5, description="最大并发数")
    delay_between_requests: float = Field(default=1.0, description="请求间隔(秒)")


class BypassResponse(BaseModel):
    """绕过响应模型"""
    success: bool
    request_id: str
    risk_level: str
    confidence: float
    fingerprint_quality: float
    bypass_techniques: List[str]
    warnings: List[str]
    execution_time: float
    timestamp: str
    strategy_used: Optional[str] = None
    modified_fingerprint: Optional[Dict[str, Any]] = None
    response_data: Optional[Dict[str, Any]] = None


class BatchBypassResponse(BaseModel):
    """批量绕过响应模型"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    success_rate: float
    total_execution_time: float
    results: List[BypassResponse]


class ServiceStatus(BaseModel):
    """服务状态模型"""
    status: str
    uptime: float
    total_requests: int
    success_rate: float
    active_devices: int
    healthy_devices: int
    current_load: float


class DeviceInfo(BaseModel):
    """设备信息模型"""
    device_id: str
    use_count: int
    success_rate: float
    last_used: Optional[str]
    is_healthy: bool
    cooldown_remaining: float


@dataclass
class APIServiceConfig:
    """API服务配置"""
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    reload: bool = False
    log_level: str = "info"
    cors_origins: List[str] = None
    max_request_size: int = 10 * 1024 * 1024  # 10MB
    rate_limit_per_minute: int = 100


class APIService:
    """API服务主类"""
    
    def __init__(self, config: APIServiceConfig = None):
        self.config = config or APIServiceConfig()
        self.logger = get_logger(self.__class__.__name__)
        
        # 初始化FastAPI应用
        self.app = FastAPI(
            title="星巴克设备指纹绕过API",
            description="Starbucks Device Fingerprint Bypass API Service",
            version="2.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 配置CORS
        if self.config.cors_origins:
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=self.config.cors_origins,
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
        
        # 初始化核心组件
        self.config_manager = ConfigManager()
        self.fingerprint_engine = DeviceFingerprintEngine(self.config_manager)
        self.concurrency_controller = ConcurrencyController()

        # 初始化反检测引擎
        from .bypass_engine import AntiDetectionEngine
        self.anti_detection_engine = AntiDetectionEngine(self.config_manager)

        # 初始化绕过引擎
        self.bypass_engine = BypassEngine(self.config_manager, self.anti_detection_engine, self.fingerprint_engine)
        
        # 服务统计
        self.service_stats = {
            'start_time': time.time(),
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0
        }
        
        # 注册路由
        self._register_routes()
        
        self.logger.info("API服务初始化完成")
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.app.get("/", response_model=Dict[str, str])
        async def root():
            """根路径"""
            return {
                "service": "星巴克设备指纹绕过API",
                "version": "2.0.0",
                "status": "running",
                "docs": "/docs"
            }
        
        @self.app.get("/health", response_model=Dict[str, Any])
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "uptime": time.time() - self.service_stats['start_time'],
                "components": {
                    "fingerprint_engine": "healthy",
                    "concurrency_controller": "healthy",
                    "bypass_engine": "healthy"
                }
            }
        
        @self.app.get("/status", response_model=ServiceStatus)
        async def get_status():
            """获取服务状态"""
            uptime = time.time() - self.service_stats['start_time']
            total_requests = self.service_stats['total_requests']
            success_rate = 0.0
            
            if total_requests > 0:
                success_rate = self.service_stats['successful_requests'] / total_requests
            
            # 获取并发控制器状态
            concurrency_status = self.concurrency_controller.get_status()
            
            return ServiceStatus(
                status="running",
                uptime=uptime,
                total_requests=total_requests,
                success_rate=success_rate,
                active_devices=concurrency_status['total_devices'],
                healthy_devices=concurrency_status['healthy_devices'],
                current_load=1.0 - (concurrency_status['available_global_slots'] /
                                  concurrency_status['max_global_concurrency'])
            )

        @self.app.get("/devices", response_model=List[DeviceInfo])
        async def get_devices():
            """获取设备列表"""
            devices = []
            device_pool = self.fingerprint_engine.fingerprint_pool
            
            for fingerprint in device_pool:
                devices.append(DeviceInfo(
                    device_id=fingerprint.device_id[:20] + "...",
                    use_count=fingerprint.use_count,
                    success_rate=fingerprint.success_rate,
                    last_used=fingerprint.last_used.isoformat() if fingerprint.last_used else None,
                    is_healthy=fingerprint.is_available(),
                    cooldown_remaining=max(0, fingerprint.get_cooldown_remaining())
                ))
            
            return devices
        
        @self.app.post("/bypass/single", response_model=BypassResponse)
        async def single_bypass(request: BypassRequest, background_tasks: BackgroundTasks):
            """单次绕过请求"""
            request_id = f"req_{int(time.time() * 1000)}"
            
            try:
                # 更新统计
                self.service_stats['total_requests'] += 1
                
                # 解析策略
                strategy = BypassStrategy(request.strategy.lower())
                
                # 执行绕过
                result = await self.bypass_engine.execute_bypass(
                    request.target_url,
                    request.method.upper(),
                    data=request.data,
                    strategy=strategy
                )
                
                # 更新成功统计
                if result['success']:
                    self.service_stats['successful_requests'] += 1
                else:
                    self.service_stats['failed_requests'] += 1

                # 构建响应
                response = BypassResponse(
                    success=result['success'],
                    request_id=request_id,
                    risk_level=result['risk_level'],
                    confidence=result['confidence'],
                    fingerprint_quality=result['fingerprint_quality'],
                    bypass_techniques=result['bypass_techniques'],
                    warnings=result['warnings'],
                    execution_time=result['execution_time'],
                    timestamp=result['timestamp'],
                    strategy_used=result.get('strategy_used'),
                    modified_fingerprint=None  # 不返回敏感的指纹信息
                )
                
                self.logger.info(f"单次绕过完成: {request_id}, 成功: {result['success']}")
                return response
                
            except Exception as e:
                self.service_stats['failed_requests'] += 1
                self.logger.error(f"单次绕过失败: {request_id}, 错误: {e}")
                raise HTTPException(status_code=500, detail=f"绕过执行失败: {str(e)}")

        @self.app.post("/bypass", response_model=BypassResponse)
        async def bypass_compatibility(request: BypassRequest, background_tasks: BackgroundTasks):
            """绕过请求（兼容性路由）"""
            return await single_bypass(request, background_tasks)

        @self.app.post("/bypass/batch", response_model=BatchBypassResponse)
        async def batch_bypass(request: BatchBypassRequest):
            """批量绕过请求"""
            start_time = time.time()
            results = []
            
            try:
                # 限制并发数
                semaphore = asyncio.Semaphore(min(request.max_concurrent, 10))
                
                async def process_single_request(bypass_req: BypassRequest, index: int):
                    async with semaphore:
                        request_id = f"batch_{int(start_time * 1000)}_{index}"
                        
                        try:
                            # 添加延迟
                            if index > 0:
                                await asyncio.sleep(request.delay_between_requests)
                            
                            # 解析策略
                            strategy = BypassStrategy(bypass_req.strategy.lower())
                            
                            # 执行绕过
                            result = await self.bypass_engine.execute_bypass(
                                bypass_req.target_url,
                                bypass_req.method.upper(),
                                data=bypass_req.data,
                                strategy=strategy
                            )

                            return BypassResponse(
                                success=result['success'],
                                request_id=request_id,
                                risk_level=result['risk_level'],
                                confidence=result['confidence'],
                                fingerprint_quality=result['fingerprint_quality'],
                                bypass_techniques=result['bypass_techniques'],
                                warnings=result['warnings'],
                                execution_time=result['execution_time'],
                                timestamp=result['timestamp'],
                                strategy_used=result.get('strategy_used'),
                                modified_fingerprint=None
                            )
                            
                        except Exception as e:
                            self.logger.error(f"批量请求失败: {request_id}, 错误: {e}")
                            return BypassResponse(
                                success=False,
                                request_id=request_id,
                                risk_level=RiskLevel.CRITICAL.value,
                                confidence=0.0,
                                fingerprint_quality=0.0,
                                bypass_techniques=[],
                                warnings=[f"执行错误: {str(e)}"],
                                execution_time=0.0,
                                timestamp=datetime.now().isoformat(),
                                strategy_used=None,
                                modified_fingerprint=None
                            )
                
                # 并发执行所有请求
                tasks = [
                    process_single_request(req, i) 
                    for i, req in enumerate(request.requests)
                ]
                results = await asyncio.gather(*tasks)
                
                # 统计结果
                total_requests = len(results)
                successful_requests = sum(1 for r in results if r.success)
                failed_requests = total_requests - successful_requests
                success_rate = successful_requests / total_requests if total_requests > 0 else 0.0
                total_execution_time = time.time() - start_time
                
                # 更新服务统计
                self.service_stats['total_requests'] += total_requests
                self.service_stats['successful_requests'] += successful_requests
                self.service_stats['failed_requests'] += failed_requests
                
                response = BatchBypassResponse(
                    total_requests=total_requests,
                    successful_requests=successful_requests,
                    failed_requests=failed_requests,
                    success_rate=success_rate,
                    total_execution_time=total_execution_time,
                    results=results
                )
                
                self.logger.info(f"批量绕过完成: {total_requests}个请求, 成功率: {success_rate:.1%}")
                return response
                
            except Exception as e:
                self.logger.error(f"批量绕过失败: {e}")
                raise HTTPException(status_code=500, detail=f"批量绕过执行失败: {str(e)}")
        
        @self.app.get("/stats", response_model=Dict[str, Any])
        async def get_stats():
            """获取统计信息"""
            bypass_stats = await self.bypass_engine.get_bypass_statistics()
            concurrency_stats = self.concurrency_controller.get_status()
            
            return {
                "service": self.service_stats,
                "bypass": bypass_stats,
                "concurrency": concurrency_stats,
                "devices": {
                    "total": len(self.fingerprint_engine.fingerprint_pool),
                    "available": len([d for d in self.fingerprint_engine.fingerprint_pool if d.is_available()])
                }
            }
        
        @self.app.post("/config/strategy", response_model=Dict[str, str])
        async def set_strategy(strategy: str):
            """设置绕过策略"""
            try:
                bypass_strategy = BypassStrategy(strategy.lower())
                self.bypass_engine.set_strategy(bypass_strategy)
                return {"message": f"策略已设置为: {strategy}", "current_strategy": strategy}
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的策略: {strategy}")
    
    async def initialize(self):
        """初始化服务"""
        try:
            # 初始化设备指纹引擎
            await self.fingerprint_engine.initialize()
            
            # 注册设备到并发控制器
            for fingerprint in self.fingerprint_engine.fingerprint_pool:
                self.concurrency_controller.register_device(fingerprint.device_id)
            
            self.logger.info("API服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"API服务初始化失败: {e}")
            raise
    
    async def start(self, host: str = "127.0.0.1", port: int = 8000):
        """启动API服务"""
        try:
            # 初始化服务
            await self.initialize()

            self.logger.info(f"启动API服务: {host}:{port}")

            # 使用uvicorn服务器启动
            import uvicorn
            config = uvicorn.Config(
                self.app,
                host=host,
                port=port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            await server.serve()

        except Exception as e:
            self.logger.error(f"API服务启动失败: {e}")
            raise

    async def stop(self):
        """停止API服务"""
        self.logger.info("API服务正在停止...")
        # 这里可以添加清理逻辑

    def run(self):
        """运行服务"""
        self.logger.info(f"启动API服务: {self.config.host}:{self.config.port}")

        uvicorn.run(
            self.app,
            host=self.config.host,
            port=self.config.port,
            workers=self.config.workers,
            reload=self.config.reload,
            log_level=self.config.log_level
        )


# 全局服务实例
api_service = None


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    global api_service
    if api_service is None:
        api_service = APIService()

        @api_service.app.on_event("startup")
        async def startup_event():
            try:
                await api_service.initialize()
                api_service.logger.info("API服务启动初始化完成")
            except Exception as e:
                api_service.logger.error(f"API服务启动初始化失败: {e}")
                raise
    return api_service.app


def get_api_service() -> APIService:
    """获取API服务实例"""
    global api_service
    if api_service is None:
        api_service = APIService()
    return api_service


if __name__ == "__main__":
    # 直接运行时的入口
    service = APIService()
    asyncio.run(service.initialize())
    service.run()
