#!/bin/bash

# 星巴克设备指纹绕过系统 - 用户完全删除脚本
# 用途: 为root用户提供完全删除用户的脚本，确保不留任何痕迹

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo ""
        echo "请使用以下方式运行:"
        echo "  sudo ./scripts/delete_user.sh"
        echo "  或"
        echo "  su -c './scripts/delete_user.sh'"
        exit 1
    fi
}

# 显示用户信息
show_user_info() {
    local username="$1"
    
    echo ""
    log_info "用户详细信息:"
    echo "  用户名: $username"
    echo "  UID: $(id -u $username 2>/dev/null || echo "未知")"
    echo "  GID: $(id -g $username 2>/dev/null || echo "未知")"
    echo "  主目录: $(getent passwd $username | cut -d: -f6 2>/dev/null || echo "未知")"
    echo "  Shell: $(getent passwd $username | cut -d: -f7 2>/dev/null || echo "未知")"
    echo "  组成员: $(groups $username 2>/dev/null || echo "未知")"
    echo ""
    
    # 显示主目录大小
    local user_home=$(getent passwd $username | cut -d: -f6 2>/dev/null)
    if [ -d "$user_home" ]; then
        local dir_size=$(du -sh "$user_home" 2>/dev/null | cut -f1)
        echo "  主目录大小: $dir_size"
    fi
    
    # 显示运行的进程
    local process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
    echo "  运行进程数: $process_count"
    
    if [ "$process_count" -gt 0 ]; then
        echo "  运行的进程:"
        ps -u $username --no-headers 2>/dev/null | head -10 | while read line; do
            echo "    $line"
        done
    fi
}

# 停止用户所有进程
stop_user_processes() {
    local username="$1"
    
    log_info "停止用户 $username 的所有进程..."
    
    # 检查是否有运行的进程
    local process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
    
    if [ "$process_count" -eq 0 ]; then
        log_info "用户 $username 没有运行的进程"
        return 0
    fi
    
    log_warning "用户 $username 有 $process_count 个运行中的进程"
    
    # 显示进程列表
    echo "进程列表:"
    ps -u $username --no-headers 2>/dev/null | head -10
    
    echo ""
    read -p "是否强制终止所有进程? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "正在终止用户进程..."
        
        # 首先尝试优雅终止
        pkill -TERM -u $username 2>/dev/null || true
        sleep 3
        
        # 检查是否还有进程
        process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
        if [ "$process_count" -gt 0 ]; then
            log_warning "仍有 $process_count 个进程运行，强制终止..."
            pkill -KILL -u $username 2>/dev/null || true
            sleep 2
        fi
        
        # 最终检查
        process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
        if [ "$process_count" -eq 0 ]; then
            log_success "所有进程已终止"
        else
            log_error "仍有 $process_count 个进程无法终止"
            ps -u $username --no-headers 2>/dev/null
        fi
    else
        log_warning "跳过进程终止，删除用户可能失败"
    fi
}

# 删除用户相关的系统服务
remove_user_services() {
    local username="$1"
    
    log_info "检查并删除用户相关的系统服务..."
    
    # 检查Supervisor配置
    if [ -f "/etc/supervisor/conf.d/starbucks_bypass.conf" ]; then
        log_info "删除Supervisor配置..."
        supervisorctl stop starbucks_bypass 2>/dev/null || true
        rm -f /etc/supervisor/conf.d/starbucks_bypass.conf
        supervisorctl reread 2>/dev/null || true
        supervisorctl update 2>/dev/null || true
        log_success "Supervisor配置已删除"
    fi
    
    # 检查Nginx配置
    if [ -f "/etc/nginx/sites-available/starbucks_bypass" ]; then
        log_info "删除Nginx配置..."
        rm -f /etc/nginx/sites-available/starbucks_bypass
        rm -f /etc/nginx/sites-enabled/starbucks_bypass
        nginx -t && systemctl reload nginx 2>/dev/null || true
        log_success "Nginx配置已删除"
    fi
    
    # 检查systemd服务
    local user_services=$(systemctl list-units --all | grep "$username" | awk '{print $1}' || true)
    if [ -n "$user_services" ]; then
        log_info "删除用户相关的systemd服务..."
        echo "$user_services" | while read service; do
            systemctl stop "$service" 2>/dev/null || true
            systemctl disable "$service" 2>/dev/null || true
            log_info "已停止服务: $service"
        done
    fi
}

# 删除用户的SSH配置
remove_ssh_config() {
    local username="$1"
    local user_home=$(getent passwd $username | cut -d: -f6 2>/dev/null)
    
    log_info "删除SSH相关配置..."
    
    # 删除用户SSH目录
    if [ -d "$user_home/.ssh" ]; then
        log_info "删除用户SSH目录: $user_home/.ssh"
        rm -rf "$user_home/.ssh"
        log_success "用户SSH目录已删除"
    fi
    
    # 从authorized_keys中删除用户的公钥（如果有的话）
    if [ -f "/root/.ssh/authorized_keys" ]; then
        # 备份原文件
        cp /root/.ssh/authorized_keys /root/.ssh/authorized_keys.backup.$(date +%Y%m%d_%H%M%S)
        
        # 删除可能包含用户名的SSH密钥
        grep -v "$username" /root/.ssh/authorized_keys > /tmp/authorized_keys.tmp 2>/dev/null || true
        if [ -s /tmp/authorized_keys.tmp ]; then
            mv /tmp/authorized_keys.tmp /root/.ssh/authorized_keys
            chmod 600 /root/.ssh/authorized_keys
            log_info "已从root的authorized_keys中清理相关条目"
        fi
        rm -f /tmp/authorized_keys.tmp
    fi
    
    # 检查SSH配置文件中的用户特定配置
    if [ -f "/etc/ssh/sshd_config" ]; then
        if grep -q "$username" /etc/ssh/sshd_config; then
            log_warning "SSH配置文件中可能包含用户 $username 的特定配置"
            echo "请手动检查 /etc/ssh/sshd_config 文件"
        fi
    fi
    
    # 删除SSH日志中的相关条目（可选）
    log_info "清理SSH日志中的用户记录..."
    if [ -f "/var/log/auth.log" ]; then
        # 不直接修改日志文件，只是提醒
        local ssh_entries=$(grep "$username" /var/log/auth.log 2>/dev/null | wc -l)
        if [ "$ssh_entries" -gt 0 ]; then
            log_info "在SSH日志中找到 $ssh_entries 条相关记录"
        fi
    fi
}

# 删除用户的cron任务
remove_user_cron() {
    local username="$1"
    
    log_info "删除用户的cron任务..."
    
    # 删除用户crontab
    crontab -u $username -r 2>/dev/null || true
    log_success "用户crontab已删除"
    
    # 检查系统cron目录
    local cron_dirs=("/etc/cron.d" "/etc/cron.daily" "/etc/cron.hourly" "/etc/cron.monthly" "/etc/cron.weekly")
    
    for dir in "${cron_dirs[@]}"; do
        if [ -d "$dir" ]; then
            local user_cron_files=$(find "$dir" -name "*$username*" 2>/dev/null || true)
            if [ -n "$user_cron_files" ]; then
                log_info "删除系统cron文件: $user_cron_files"
                echo "$user_cron_files" | xargs rm -f
            fi
        fi
    done
}

# 删除用户的邮件和日志
remove_user_mail_logs() {
    local username="$1"
    
    log_info "删除用户邮件和日志..."
    
    # 删除邮件
    rm -rf "/var/mail/$username" 2>/dev/null || true
    rm -rf "/var/spool/mail/$username" 2>/dev/null || true
    rm -rf "/home/<USER>/Maildir" 2>/dev/null || true
    
    # 删除用户特定的日志文件
    local log_files=$(find /var/log -name "*$username*" 2>/dev/null || true)
    if [ -n "$log_files" ]; then
        log_info "删除用户相关日志文件..."
        echo "$log_files" | while read logfile; do
            rm -f "$logfile"
            log_info "已删除: $logfile"
        done
    fi
    
    log_success "邮件和日志清理完成"
}

# 删除用户的临时文件
remove_user_temp_files() {
    local username="$1"
    
    log_info "删除用户临时文件..."
    
    # 删除/tmp中的用户文件
    find /tmp -user $username -exec rm -rf {} + 2>/dev/null || true
    
    # 删除/var/tmp中的用户文件
    find /var/tmp -user $username -exec rm -rf {} + 2>/dev/null || true
    
    # 删除/run中的用户文件
    find /run -user $username -exec rm -rf {} + 2>/dev/null || true
    
    log_success "临时文件清理完成"
}

# 完全删除用户
delete_user_completely() {
    local username="$1"
    
    log_info "完全删除用户: $username"
    
    # 删除用户账户和主目录
    if userdel -r $username 2>/dev/null; then
        log_success "用户账户和主目录已删除"
    else
        log_warning "userdel命令执行失败，尝试手动清理..."
        
        # 手动删除用户
        userdel $username 2>/dev/null || true
        
        # 手动删除主目录
        local user_home=$(getent passwd $username | cut -d: -f6 2>/dev/null || echo "/home/<USER>")
        if [ -d "$user_home" ]; then
            rm -rf "$user_home"
            log_success "手动删除主目录: $user_home"
        fi
    fi
    
    # 删除用户组（如果是私有组）
    if getent group $username >/dev/null 2>&1; then
        groupdel $username 2>/dev/null || true
        log_success "用户组已删除"
    fi
}

# 验证删除结果
verify_deletion() {
    local username="$1"
    
    log_info "验证删除结果..."
    
    local issues=0
    
    # 检查用户是否还存在
    if id "$username" >/dev/null 2>&1; then
        log_error "用户 $username 仍然存在"
        ((issues++))
    else
        log_success "用户账户已完全删除"
    fi
    
    # 检查主目录
    local user_home="/home/<USER>"
    if [ -d "$user_home" ]; then
        log_error "用户主目录仍然存在: $user_home"
        ((issues++))
    else
        log_success "用户主目录已删除"
    fi
    
    # 检查进程
    local process_count=$(ps -u $username --no-headers 2>/dev/null | wc -l)
    if [ "$process_count" -gt 0 ]; then
        log_error "仍有 $process_count 个用户进程在运行"
        ((issues++))
    else
        log_success "没有用户进程在运行"
    fi
    
    # 检查邮件
    if [ -f "/var/mail/$username" ] || [ -f "/var/spool/mail/$username" ]; then
        log_warning "用户邮件文件仍然存在"
        ((issues++))
    else
        log_success "用户邮件已清理"
    fi
    
    # 检查crontab
    if crontab -u $username -l >/dev/null 2>&1; then
        log_warning "用户crontab仍然存在"
        ((issues++))
    else
        log_success "用户crontab已清理"
    fi
    
    echo ""
    if [ $issues -eq 0 ]; then
        log_success "[成功] 用户 $username 已完全删除，未发现残留"
    else
        log_warning "[警告]  发现 $issues 个潜在问题，可能需要手动清理"
    fi
}

# 主函数
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  用户完全删除工具 (Root专用)${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # 检查root权限
    check_root
    
    # 固定删除starbucks用户
    local username="starbucks"
    
    echo -e "${CYAN}此脚本将完全删除用户及其所有相关数据${NC}"
    echo ""
    echo "[警告]  警告: 此操作不可逆转！"
    echo ""
    echo "将要删除的用户: $username"
    echo ""
    echo "删除内容包括:"
    echo "  - 用户账户和密码"
    echo "  - 用户主目录和所有文件"
    echo "  - SSH配置和密钥"
    echo "  - 系统服务配置"
    echo "  - Cron任务"
    echo "  - 邮件和日志"
    echo "  - 临时文件"
    echo "  - 运行中的进程"
    echo ""
    
    # 检查用户是否存在
    if ! id "$username" >/dev/null 2>&1; then
        log_info "用户 $username 不存在，无需删除"
        exit 0
    fi
    
    # 显示用户信息
    show_user_info "$username"
    
    # 最终确认
    echo ""
    log_error "[警告]  最终确认 [警告]"
    echo "即将完全删除用户 $username 和所有相关数据！"
    echo ""
    read -p "确认删除? 输入 'DELETE-EVERYTHING' 继续: " confirm
    
    if [[ "$confirm" != "DELETE-EVERYTHING" ]]; then
        log_info "确认失败，操作取消"
        exit 0
    fi
    
    echo ""
    log_info "开始删除用户 $username..."
    echo ""
    
    # 执行删除步骤
    stop_user_processes "$username"
    echo ""
    
    remove_user_services "$username"
    echo ""
    
    remove_ssh_config "$username"
    echo ""
    
    remove_user_cron "$username"
    echo ""
    
    remove_user_mail_logs "$username"
    echo ""
    
    remove_user_temp_files "$username"
    echo ""
    
    delete_user_completely "$username"
    echo ""
    
    verify_deletion "$username"
    
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  删除完成${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    log_success "用户 $username 已完全删除！"
    log_info "系统已恢复到用户创建前的状态"
}

# 执行主函数
main "$@"
