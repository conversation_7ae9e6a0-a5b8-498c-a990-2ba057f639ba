#!/bin/bash

echo "🔧 修复API服务初始化问题 - 问题9..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 4. 备份当前文件
echo "💾 备份当前文件..."
cp src/core/api_service.py src/core/api_service.py.backup.$(date +%Y%m%d_%H%M%S)
cp src/core/device_fingerprint_engine.py src/core/device_fingerprint_engine.py.backup.$(date +%Y%m%d_%H%M%S)

# 5. 修复API服务文件
echo "🔧 修复API服务初始化..."
python3 << 'FIX_API_SERVICE'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

# 读取API服务文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 修复ConcurrencyController初始化
if 'self.concurrency_controller = ConcurrencyController()' in content:
    content = content.replace(
        'self.concurrency_controller = ConcurrencyController()',
        'self.concurrency_controller = ConcurrencyController(self.config_manager)'
    )
    print("✅ 修复了ConcurrencyController初始化")

# 移除任何错误的max_concurrent参数
if 'ConcurrencyController(max_concurrent=' in content:
    import re
    content = re.sub(
        r'ConcurrencyController\(max_concurrent=\d+\)',
        'ConcurrencyController(self.config_manager)',
        content
    )
    print("✅ 移除了错误的max_concurrent参数")

# 写回文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("✅ API服务文件修复完成")
FIX_API_SERVICE

# 6. 修复设备指纹引擎路径
echo "🔧 修复设备指纹引擎路径..."
python3 << 'FIX_DEVICE_PATH'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

# 读取设备指纹引擎文件
with open('src/core/device_fingerprint_engine.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 修复设备配置文件路径
old_path = 'os.path.join(os.getcwd(), "src/config/device_profiles.json")'
new_path = 'os.path.join(os.path.dirname(os.path.dirname(__file__)), "config/device_profiles.json")'

if old_path in content:
    content = content.replace(old_path, new_path)
    print("✅ 修复了设备配置文件路径")
else:
    print("ℹ️ 设备配置文件路径已经正确")

# 写回文件
with open('src/core/device_fingerprint_engine.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("✅ 设备指纹引擎文件修复完成")
FIX_DEVICE_PATH

# 7. 验证修复
echo "🔍 验证修复后的代码..."
python3 << 'VERIFY_FIX'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

print("测试模块导入:")
try:
    from config.config_manager import ConfigManager
    print("✅ ConfigManager 导入成功")
    
    from core.concurrency_controller import ConcurrencyController
    print("✅ ConcurrencyController 导入成功")
    
    # 测试正确的初始化方式
    config_manager = ConfigManager()
    concurrency_controller = ConcurrencyController(config_manager)
    print("✅ ConcurrencyController 初始化成功")
    
    from core.device_fingerprint_engine import DeviceFingerprintEngine
    print("✅ DeviceFingerprintEngine 导入成功")
    
    # 测试设备指纹引擎初始化
    fingerprint_engine = DeviceFingerprintEngine(config_manager)
    print("✅ DeviceFingerprintEngine 初始化成功")
    
    from core.api_service import create_app
    print("✅ create_app 导入成功")
    
    app = create_app()
    print("✅ app 创建成功")
    print(f"app类型: {type(app)}")
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
VERIFY_FIX

# 8. 重新加载supervisor配置
echo "🔄 重新加载supervisor配置..."
sudo supervisorctl reread
sudo supervisorctl update

# 9. 启动服务
echo "🚀 启动服务..."
sudo supervisorctl start starbucks_bypass

# 10. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 11. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 12. 测试API接口
echo "🧪 测试API接口..."

# 测试健康检查
echo "1. 测试健康检查:"
curl -s http://localhost:8000/health && echo "✅ 健康检查成功" || echo "❌ 健康检查失败"

# 测试信息接口
echo "2. 测试信息接口:"
curl -s http://localhost:8000/info && echo "✅ 信息接口成功" || echo "❌ 信息接口失败"

# 测试设备接口
echo "3. 测试设备接口:"
curl -s http://localhost:8000/devices && echo "✅ 设备接口成功" || echo "❌ 设备接口失败"

# 测试统计接口
echo "4. 测试统计接口:"
curl -s http://localhost:8000/stats && echo "✅ 统计接口成功" || echo "❌ 统计接口失败"

# 测试单次绕过
echo "5. 测试单次绕过:"
curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{"target_url": "https://httpbin.org/get", "method": "GET"}' && echo "✅ 单次绕过成功" || echo "❌ 单次绕过失败"

echo "✅ API服务修复完成"

# 13. 显示最终状态
echo ""
echo "🎯 修复总结:"
echo "================================"
echo "✅ ConcurrencyController 初始化修复"
echo "✅ 设备配置文件路径修复"
echo "✅ API服务重新启动"
echo "✅ 基础功能测试完成"
echo "================================"

# 14. 显示服务信息
echo ""
echo "📋 服务信息:"
echo "服务状态: $(sudo supervisorctl status starbucks_bypass | awk '{print $2}')"
echo "API地址: http://localhost:8000"
echo "API文档: http://localhost:8000/docs"
echo "健康检查: http://localhost:8000/health"

echo ""
echo "🔍 如果仍有问题，请检查日志："
echo "sudo supervisorctl tail -f starbucks_bypass"
