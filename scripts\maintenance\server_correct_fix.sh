#!/bin/bash

# 正确修复服务器API服务文件
echo "🔧 正确修复服务器API服务文件..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 4. 从本地项目重新创建正确的API服务文件
echo "🔧 重新创建正确的API服务文件..."
cat > src/core/api_service_fixed.py << 'API_SERVICE_CONTENT'
import asyncio
import time
import logging
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from .device_fingerprint_engine import DeviceFingerprintEngine, DeviceFingerprint
from .bypass_engine import BypassEngine, AntiDetectionEngine, BypassStrategy
from .concurrency_controller import ConcurrencyController
from .monitor import Monitor


# Pydantic模型定义
class BypassRequest(BaseModel):
    target_url: str = Field(..., description="目标URL")
    method: str = Field(default="GET", description="HTTP方法")
    headers: Optional[Dict[str, str]] = Field(default=None, description="请求头")
    data: Optional[Dict[str, Any]] = Field(default=None, description="请求数据")
    strategy: Optional[str] = Field(default="adaptive", description="绕过策略")


class BatchBypassRequest(BaseModel):
    requests: List[BypassRequest] = Field(..., description="批量请求列表")
    max_concurrent: Optional[int] = Field(default=5, description="最大并发数")


class BypassResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    request_id: str = Field(..., description="请求ID")
    confidence: float = Field(..., description="置信度")
    risk_level: str = Field(..., description="风险等级")
    fingerprint_quality: float = Field(..., description="指纹质量")
    bypass_techniques: List[str] = Field(..., description="使用的绕过技术")
    warnings: List[str] = Field(..., description="警告信息")
    execution_time: float = Field(..., description="执行时间")
    timestamp: float = Field(..., description="时间戳")
    strategy_used: Optional[str] = Field(None, description="使用的策略")
    modified_fingerprint: Optional[Dict[str, Any]] = Field(None, description="修改后的指纹")


class BatchBypassResponse(BaseModel):
    results: List[BypassResponse] = Field(..., description="批量结果")
    total_requests: int = Field(..., description="总请求数")
    successful_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    execution_time: float = Field(..., description="总执行时间")


class DeviceInfo(BaseModel):
    device_id: str = Field(..., description="设备ID")
    is_active: bool = Field(..., description="是否激活")
    success_rate: float = Field(..., description="成功率")
    last_used: Optional[float] = Field(None, description="最后使用时间")
    cooldown_remaining: float = Field(..., description="剩余冷却时间")


class DevicesResponse(BaseModel):
    devices: List[DeviceInfo] = Field(..., description="设备列表")
    total: int = Field(..., description="总设备数")
    active: int = Field(..., description="活跃设备数")
    available: int = Field(..., description="可用设备数")


class StatsResponse(BaseModel):
    service: Dict[str, Any] = Field(..., description="服务统计")
    devices: Dict[str, Any] = Field(..., description="设备统计")
    bypass: Dict[str, Any] = Field(..., description="绕过统计")


class HealthResponse(BaseModel):
    status: str = Field(..., description="服务状态")
    timestamp: float = Field(..., description="检查时间")
    components: Dict[str, str] = Field(..., description="组件状态")


class InfoResponse(BaseModel):
    name: str = Field(..., description="服务名称")
    version: str = Field(..., description="版本")
    description: str = Field(..., description="描述")
    uptime: float = Field(..., description="运行时间")


class APIServiceConfig:
    def __init__(self):
        self.host = "0.0.0.0"
        self.port = 8000
        self.log_level = "INFO"


class APIService:
    def __init__(self, config: APIServiceConfig = None):
        self.config = config or APIServiceConfig()
        self.config_manager = ConfigManager()
        self.logger = get_logger(self.__class__.__name__)
        
        # 初始化FastAPI应用
        self.app = FastAPI(
            title="星巴克设备指纹绕过API",
            description="Starbucks Device Fingerprint Bypass API Service",
            version="2.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 添加CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 初始化组件
        self.fingerprint_engine = None
        self.concurrency_controller = None
        self.monitor = None
        self.anti_detection_engine = None
        self.bypass_engine = None
        
        # 服务统计
        self.service_stats = {
            'start_time': time.time(),
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0
        }
        
        # 注册路由
        self._register_routes()
        
        self.logger.info("API服务初始化完成")

    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("开始初始化API服务组件...")
            
            # 初始化设备指纹引擎
            self.fingerprint_engine = DeviceFingerprintEngine()
            await self.fingerprint_engine.initialize()
            self.logger.info("设备指纹引擎初始化完成")
            
            # 初始化并发控制器
            self.concurrency_controller = ConcurrencyController(max_concurrent=10)
            
            # 注册设备到并发控制器
            for fingerprint in self.fingerprint_engine.fingerprint_pool:
                self.concurrency_controller.register_device(fingerprint.device_id)
            
            self.logger.info("并发控制器初始化完成")
            
            # 初始化监控器
            self.monitor = Monitor()
            self.logger.info("监控器初始化完成")
            
            # 初始化反检测引擎
            self.anti_detection_engine = AntiDetectionEngine(self.config_manager)
            self.logger.info("反检测引擎初始化完成")
            
            # 初始化绕过引擎
            self.bypass_engine = BypassEngine(self.config_manager, self.anti_detection_engine, self.fingerprint_engine)
            self.logger.info("绕过引擎初始化完成")
            
            self.logger.info("所有组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"API服务初始化失败: {e}")
            raise

    def _register_routes(self):
        """注册API路由"""
        
        @self.app.get("/health", response_model=HealthResponse)
        async def health_check():
            """健康检查"""
            return HealthResponse(
                status="healthy",
                timestamp=time.time(),
                components={
                    "fingerprint_engine": "healthy" if self.fingerprint_engine else "not_initialized",
                    "bypass_engine": "healthy" if self.bypass_engine else "not_initialized",
                    "concurrency_controller": "healthy" if self.concurrency_controller else "not_initialized",
                    "monitor": "healthy" if self.monitor else "not_initialized"
                }
            )

        @self.app.get("/info", response_model=InfoResponse)
        async def get_info():
            """获取服务信息"""
            return InfoResponse(
                name="Starbucks Bypass API",
                version="2.0.0",
                description="星巴克设备指纹绕过API服务",
                uptime=time.time() - self.service_stats['start_time']
            )

        @self.app.get("/devices", response_model=DevicesResponse)
        async def get_devices():
            """获取设备列表"""
            if not self.fingerprint_engine:
                raise HTTPException(status_code=503, detail="设备指纹引擎未初始化")
            
            devices = []
            for fingerprint in self.fingerprint_engine.fingerprint_pool:
                devices.append(DeviceInfo(
                    device_id=fingerprint.device_id,
                    is_active=fingerprint.is_active,
                    success_rate=fingerprint.success_rate,
                    last_used=fingerprint.last_used,
                    cooldown_remaining=fingerprint.get_cooldown_remaining()
                ))
            
            active_count = sum(1 for d in devices if d.is_active)
            available_count = sum(1 for fingerprint in self.fingerprint_engine.fingerprint_pool if fingerprint.is_available())
            
            return DevicesResponse(
                devices=devices,
                total=len(devices),
                active=active_count,
                available=available_count
            )

        @self.app.get("/stats", response_model=StatsResponse)
        async def get_stats():
            """获取统计信息"""
            if not self.bypass_engine:
                raise HTTPException(status_code=503, detail="绕过引擎未初始化")
            
            bypass_stats = await self.bypass_engine.get_bypass_statistics()
            
            return StatsResponse(
                service=self.service_stats,
                devices={
                    "total": len(self.fingerprint_engine.fingerprint_pool) if self.fingerprint_engine else 0,
                    "active": sum(1 for fp in self.fingerprint_engine.fingerprint_pool if fp.is_active) if self.fingerprint_engine else 0
                },
                bypass=bypass_stats
            )

        @self.app.post("/bypass/single", response_model=BypassResponse)
        async def single_bypass(request: BypassRequest):
            """单次绕过请求"""
            if not self.bypass_engine:
                raise HTTPException(status_code=503, detail="绕过引擎未初始化")
            
            try:
                self.service_stats['total_requests'] += 1
                
                # 解析策略
                strategy = None
                if request.strategy:
                    try:
                        strategy = BypassStrategy(request.strategy)
                    except ValueError:
                        strategy = BypassStrategy.ADAPTIVE
                
                # 执行绕过
                result = await self.bypass_engine.execute_bypass(
                    fingerprint_or_url=request.target_url,
                    strategy_or_method=request.method,
                    data=request.data,
                    strategy=strategy
                )
                
                if result['success']:
                    self.service_stats['successful_requests'] += 1
                else:
                    self.service_stats['failed_requests'] += 1
                
                return BypassResponse(
                    success=result['success'],
                    request_id=f"req_{int(time.time() * 1000)}",
                    confidence=result['confidence'],
                    risk_level=result['risk_level'],
                    fingerprint_quality=result['fingerprint_quality'],
                    bypass_techniques=result['bypass_techniques'],
                    warnings=result['warnings'],
                    execution_time=result['execution_time'],
                    timestamp=result['timestamp'],
                    strategy_used=result['strategy_used'],
                    modified_fingerprint=result['modified_fingerprint']
                )
                
            except Exception as e:
                self.service_stats['failed_requests'] += 1
                self.logger.error(f"单次绕过执行失败: {e}")
                raise HTTPException(status_code=500, detail=f"绕过执行失败: {str(e)}")

        @self.app.post("/bypass/batch", response_model=BatchBypassResponse)
        async def batch_bypass(request: BatchBypassRequest):
            """批量绕过请求"""
            if not self.bypass_engine:
                raise HTTPException(status_code=503, detail="绕过引擎未初始化")
            
            try:
                start_time = time.time()
                results = []
                successful_count = 0
                failed_count = 0
                
                for bypass_request in request.requests:
                    try:
                        self.service_stats['total_requests'] += 1
                        
                        # 解析策略
                        strategy = None
                        if bypass_request.strategy:
                            try:
                                strategy = BypassStrategy(bypass_request.strategy)
                            except ValueError:
                                strategy = BypassStrategy.ADAPTIVE
                        
                        # 执行绕过
                        result = await self.bypass_engine.execute_bypass(
                            fingerprint_or_url=bypass_request.target_url,
                            strategy_or_method=bypass_request.method,
                            data=bypass_request.data,
                            strategy=strategy
                        )
                        
                        if result['success']:
                            successful_count += 1
                            self.service_stats['successful_requests'] += 1
                        else:
                            failed_count += 1
                            self.service_stats['failed_requests'] += 1
                        
                        results.append(BypassResponse(
                            success=result['success'],
                            request_id=f"batch_req_{int(time.time() * 1000)}_{len(results)}",
                            confidence=result['confidence'],
                            risk_level=result['risk_level'],
                            fingerprint_quality=result['fingerprint_quality'],
                            bypass_techniques=result['bypass_techniques'],
                            warnings=result['warnings'],
                            execution_time=result['execution_time'],
                            timestamp=result['timestamp'],
                            strategy_used=result['strategy_used'],
                            modified_fingerprint=result['modified_fingerprint']
                        ))
                        
                    except Exception as e:
                        failed_count += 1
                        self.service_stats['failed_requests'] += 1
                        self.logger.error(f"批量绕过中的单个请求失败: {e}")
                        
                        results.append(BypassResponse(
                            success=False,
                            request_id=f"batch_req_{int(time.time() * 1000)}_{len(results)}",
                            confidence=0.0,
                            risk_level="high",
                            fingerprint_quality=0.0,
                            bypass_techniques=[],
                            warnings=[f"执行失败: {str(e)}"],
                            execution_time=0.0,
                            timestamp=time.time(),
                            strategy_used="none",
                            modified_fingerprint=None
                        ))
                
                execution_time = time.time() - start_time
                
                return BatchBypassResponse(
                    results=results,
                    total_requests=len(request.requests),
                    successful_requests=successful_count,
                    failed_requests=failed_count,
                    execution_time=execution_time
                )
                
            except Exception as e:
                self.logger.error(f"批量绕过执行失败: {e}")
                raise HTTPException(status_code=500, detail=f"批量绕过执行失败: {str(e)}")

    def run(self):
        """运行API服务"""
        uvicorn.run(
            self.app,
            host=self.config.host,
            port=self.config.port,
            log_level=self.config.log_level.lower()
        )


# 全局服务实例
api_service = None


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    global api_service
    if api_service is None:
        api_service = APIService()

        @api_service.app.on_event("startup")
        async def startup_event():
            try:
                await api_service.initialize()
                api_service.logger.info("API服务启动初始化完成")
            except Exception as e:
                api_service.logger.error(f"API服务启动初始化失败: {e}")
                raise
    return api_service.app


def get_api_service() -> APIService:
    """获取API服务实例"""
    global api_service
    if api_service is None:
        api_service = APIService()
    return api_service


if __name__ == "__main__":
    # 直接运行时的入口
    service = APIService()
    asyncio.run(service.initialize())
    service.run()

# 导出app供uvicorn使用
app = create_app()
API_SERVICE_CONTENT

# 5. 替换原文件
echo "🔄 替换原API服务文件..."
mv src/core/api_service.py src/core/api_service.py.broken_backup.$(date +%Y%m%d_%H%M%S)
mv src/core/api_service_fixed.py src/core/api_service.py

# 6. 验证语法
echo "🔍 验证语法..."
cd src
python3 -c "
import ast
with open('core/api_service.py', 'r') as f:
    content = f.read()
try:
    ast.parse(content)
    print('✅ 语法验证通过')
except SyntaxError as e:
    print(f'❌ 语法错误: 第{e.lineno}行: {e.text.strip() if e.text else \"未知\"}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 语法验证失败"
    deactivate
    exit 1
fi

# 7. 测试模块导入
echo "🔍 测试模块导入..."
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    from core.api_service import app, create_app
    print('✅ API服务模块导入成功')
except Exception as e:
    print(f'❌ API服务模块导入失败: {e}')
    exit(1)

try:
    from core.bypass_engine import BypassEngine
    print('✅ 绕过引擎模块导入成功')
except Exception as e:
    print(f'❌ 绕过引擎模块导入失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 模块导入测试失败"
    deactivate
    exit 1
fi

# 8. 启动服务
echo "🚀 启动服务..."
cd /home/<USER>/apps/starbucks_bypass_tester
sudo supervisorctl start starbucks_bypass

# 9. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 10. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 11. 检查启动日志
echo "📋 检查启动日志..."
echo "=== 最近的输出日志 ==="
tail -15 logs/output.log 2>/dev/null || echo "无输出日志"
echo ""
echo "=== 最近的错误日志 ==="
tail -10 logs/error.log 2>/dev/null || echo "无错误日志"

# 12. 测试API接口
echo "🧪 测试API接口..."

# 健康检查
echo "测试健康检查:"
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
    echo "$health_response"
else
    echo "❌ 健康检查失败: $health_response"
fi

# 单次绕过
echo ""
echo "测试单次绕过:"
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
fi

# 13. 总结
echo ""
echo "🎯 修复总结:"
if echo "$health_response" | grep -q '"status"' && echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ API服务修复成功！"
    echo "🎉 现在可以运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo "❌ API服务仍有问题，需要进一步调试"
    echo "查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
