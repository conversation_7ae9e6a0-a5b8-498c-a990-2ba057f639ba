#!/usr/bin/env python3
"""
验证 bypass_engine.py 类型注解修复
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent / "starbucks_bypass_tester"
sys.path.insert(0, str(project_root / "src"))

def test_imports():
    """测试模块导入"""
    try:
        from core.bypass_engine import (
            BypassEngine, 
            AntiDetectionEngine, 
            DeviceFingerprintEngine, 
            DeviceFingerprint,
            BypassStrategy,
            RiskLevel
        )
        print("✅ 所有核心类导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_syntax():
    """测试语法正确性"""
    try:
        import py_compile
        bypass_engine_path = project_root / "src" / "core" / "bypass_engine.py"
        py_compile.compile(str(bypass_engine_path), doraise=True)
        print("✅ bypass_engine.py 语法检查通过")
        return True
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def test_type_annotations():
    """测试类型注解修复"""
    bypass_engine_path = project_root / "src" / "core" / "bypass_engine.py"

    with open(bypass_engine_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查修复1: DeviceFingerprintEngine
    if "fingerprint_engine: 'DeviceFingerprintEngine'" in content:
        print("✅ DeviceFingerprintEngine 类型注解修复正确")
        fix1_ok = True
    else:
        print("❌ DeviceFingerprintEngine 类型注解修复失败")
        fix1_ok = False

    # 检查修复2: DeviceFingerprint
    if "fingerprint: 'DeviceFingerprint'" in content:
        print("✅ DeviceFingerprint 类型注解修复正确")
        fix2_ok = True
    else:
        print("❌ DeviceFingerprint 类型注解修复失败")
        fix2_ok = False

    return fix1_ok and fix2_ok

def test_api_service_fixes():
    """测试API服务修复"""
    api_service_path = project_root / "src" / "core" / "api_service.py"

    with open(api_service_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查修复1: get_bypass_statistics方法调用
    if "await self.bypass_engine.get_bypass_statistics()" in content:
        print("✅ API统计方法调用修复正确")
        fix1_ok = True
    else:
        print("❌ API统计方法调用修复失败")
        fix1_ok = False

    # 检查修复2: 参数传递方式
    if "data=request.data," in content and "strategy=strategy" in content:
        print("✅ API参数传递修复正确")
        fix2_ok = True
    else:
        print("❌ API参数传递修复失败")
        fix2_ok = False

    # 检查修复3: 字典访问方式
    if "result['success']" in content and "result['risk_level']" in content:
        print("✅ API返回值访问修复正确")
        fix3_ok = True
    else:
        print("❌ API返回值访问修复失败")
        fix3_ok = False

    return fix1_ok and fix2_ok and fix3_ok

def test_device_fingerprint_methods():
    """测试DeviceFingerprint类方法修复"""
    device_fingerprint_path = project_root / "src" / "core" / "device_fingerprint_engine.py"

    with open(device_fingerprint_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查修复1: is_available方法
    if "def is_available(self) -> bool:" in content:
        print("✅ DeviceFingerprint.is_available()方法修复正确")
        fix1_ok = True
    else:
        print("❌ DeviceFingerprint.is_available()方法修复失败")
        fix1_ok = False

    # 检查修复2: get_cooldown_remaining方法
    if "def get_cooldown_remaining(self) -> float:" in content:
        print("✅ DeviceFingerprint.get_cooldown_remaining()方法修复正确")
        fix2_ok = True
    else:
        print("❌ DeviceFingerprint.get_cooldown_remaining()方法修复失败")
        fix2_ok = False

    # 检查修复3: success_rate字段
    if "success_rate: float = 1.0" in content:
        print("✅ DeviceFingerprint.success_rate字段修复正确")
        fix3_ok = True
    else:
        print("❌ DeviceFingerprint.success_rate字段修复失败")
        fix3_ok = False

    return fix1_ok and fix2_ok and fix3_ok

def test_api_syntax():
    """测试API服务语法正确性"""
    try:
        import py_compile
        api_service_path = project_root / "src" / "core" / "api_service.py"
        py_compile.compile(str(api_service_path), doraise=True)
        print("✅ api_service.py 语法检查通过")
        return True
    except Exception as e:
        print(f"❌ API服务语法检查失败: {e}")
        return False

def main():
    print("🔍 验证项目修复状态...")
    print("=" * 60)

    all_passed = True

    # 测试bypass_engine语法
    if not test_syntax():
        all_passed = False

    # 测试API服务语法
    if not test_api_syntax():
        all_passed = False

    # 测试类型注解修复
    if not test_type_annotations():
        all_passed = False

    # 测试API服务修复
    if not test_api_service_fixes():
        all_passed = False

    # 测试DeviceFingerprint方法修复
    if not test_device_fingerprint_methods():
        all_passed = False

    # 测试导入
    if not test_imports():
        all_passed = False

    print("=" * 60)
    if all_passed:
        print("🎉 所有检查通过！项目修复成功")
        print("✅ bypass_engine.py 类型注解修复完成")
        print("✅ api_service.py 方法调用修复完成")
        print("✅ device_fingerprint_engine.py 方法添加完成")
        print("✅ 可以安全部署到服务器")
    else:
        print("❌ 部分检查失败！请检查上述错误")

    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
