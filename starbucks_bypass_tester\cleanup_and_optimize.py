#!/usr/bin/env python3
"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]F5 Shape[符号][符号][符号][符号]
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Set
import re

class StarbucksSystemCleaner:
    """[符号][符号][符号][符号][符号][符号][符号][符号]"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.device_config_path = self.project_root / "src/config/device_profiles.json"
        self.cleaned_files = []
        self.removed_files = []
        self.optimized_files = []
        
    def clean_device_profiles(self):
        """[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号][符号][符号]...")
        
        if not self.device_config_path.exists():
            print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号]: {self.device_config_path}")
            return
            
        # [符号][符号][符号][符号][符号]
        backup_path = self.device_config_path.with_suffix('.json.backup')
        shutil.copy2(self.device_config_path, backup_path)
        print(f"[符号][符号][符号][符号][符号][符号][符号]: {backup_path}")
        
        # [符号][符号][符号][符号]
        with open(self.device_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # [符号][符号][符号][符号][符号][符号]
        devices = config.get('devices', [])
        seen_device_ids = set()
        unique_devices = []
        duplicates = []
        
        for device in devices:
            device_id = device.get('device_id')
            if device_id in seen_device_ids:
                duplicates.append(device_id)
                print(f"[符号][符号][符号][符号][符号][符号]: {device_id}")
            else:
                seen_device_ids.add(device_id)
                unique_devices.append(device)
        
        print(f"[符号][符号][符号][符号][符号]: {len(devices)}")
        print(f"[符号][符号][符号][符号][符号]: {len(unique_devices)}")
        print(f"[符号][符号][符号][符号]: {duplicates}")
        
        # [符号][符号][符号]30[符号][符号][符号]
        if len(unique_devices) < 30:
            print(f"[符号][符号]: [符号][符号][符号][符号][符号]{len(unique_devices)}[符号][符号][符号][符号][符号][符号]30[符号][符号][符号]")
            # [符号][符号][符号]30[符号][符号][符号][符号][符号]
            unique_devices = unique_devices[:30]
        elif len(unique_devices) > 30:
            # [符号][符号][符号][符号]30[符号]
            unique_devices = unique_devices[:30]
            print(f"[符号][符号][符号]30[符号][符号][符号]")
        
        # [符号][符号][符号][符号]
        config['devices'] = unique_devices
        config['device_pool']['total_devices'] = len(unique_devices)
        
        # [符号][符号][符号][符号]
        with open(self.device_config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]{len(unique_devices)}[符号][符号][符号]")
        self.cleaned_files.append(str(self.device_config_path))
    
    def remove_redundant_files(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号]...")
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        redundant_patterns = [
            # [符号][符号][符号][符号]
            "*.tmp", "*.temp", "*.bak", "*.backup",
            # [符号][符号][符号][符号]
            "*.pyc", "*.pyo", "*.pyd",
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            "*.log.old", "*.log.1", "*.log.2",
            # [符号][符号][符号][符号][符号][符号]
            ".pytest_cache", "__pycache__",
            # [符号][符号][符号][符号][符号][符号][符号]
            "*.swp", "*.swo", "*~",
            # [符号][符号][符号][符号]
            ".DS_Store", "Thumbs.db"
        ]
        
        # [符号][符号][符号][符号][符号][符号][符号][符号]
        redundant_files = [
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
            "[符号][符号][符号][符号]9.md",
            "[符号][符号][符号][符号]9_[符号][符号][符号].md", 
            "[符号][符号][符号][符号][符号][符号][符号]_[符号][符号]9.md",
            # [符号][符号][符号][符号][符号]
            "scripts/fix_api_initialization_issue9_complete.sh"
        ]
        
        removed_count = 0
        
        # [符号][符号][符号][符号][符号][符号][符号][符号][符号]
        for pattern in redundant_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    try:
                        file_path.unlink()
                        self.removed_files.append(str(file_path))
                        removed_count += 1
                        print(f"[符号][符号]: {file_path}")
                    except Exception as e:
                        print(f"[符号][符号][符号][符号] {file_path}: {e}")
        
        # [符号][符号][符号][符号][符号][符号]
        for file_name in redundant_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                try:
                    file_path.unlink()
                    self.removed_files.append(str(file_path))
                    removed_count += 1
                    print(f"[符号][符号]: {file_path}")
                except Exception as e:
                    print(f"[符号][符号][符号][符号] {file_path}: {e}")
        
        print(f"[符号][符号][符号] {removed_count} [符号][符号][符号][符号][符号]")
    
    def optimize_f5_shape_implementation(self):
        """[符号][符号]F5 Shape[符号][符号][符号][符号]"""
        print("[符号][符号]F5 Shape[符号][符号][符号][符号]...")
        
        # [符号][符号]F5 Shape[符号][符号][符号][符号]
        f5_files = [
            "src/core/device_fingerprint_engine.py",
            "src/core/header_generator.py",
            "src/core/bypass_engine.py"
        ]
        
        for file_name in f5_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self._optimize_python_file(file_path)
    
    def _optimize_python_file(self, file_path: Path):
        """[符号][符号]Python[符号][符号]"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_lines = len(content.splitlines())
            
            # [符号][符号][符号][符号][符号][符号][符号]
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
            
            # [符号][符号][符号][符号][符号][符号]
            content = re.sub(r'[ \t]+$', '', content, flags=re.MULTILINE)
            
            # [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
            if not content.endswith('\n'):
                content += '\n'
            
            # [符号][符号][符号][符号]
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            new_lines = len(content.splitlines())
            if original_lines != new_lines:
                print(f"[符号][符号] {file_path}: {original_lines} -> {new_lines} [符号]")
                self.optimized_files.append(str(file_path))
                
        except Exception as e:
            print(f"[符号][符号][符号][符号][符号][符号] {file_path}: {e}")
    
    def check_f5_dependencies(self):
        """[符号][符号]F5 Shape[符号][符号][符号][符号]"""
        print("[符号][符号]F5 Shape[符号][符号][符号][符号]...")
        
        requirements_path = self.project_root / "requirements.txt"
        if not requirements_path.exists():
            print("requirements.txt [符号][符号][符号]")
            return
        
        with open(requirements_path, 'r', encoding='utf-8') as f:
            requirements = f.read()
        
        # [符号][符号][符号][符号][符号][符号][符号]
        required_deps = [
            'requests',
            'aiohttp', 
            'fastapi',
            'cryptography',
            'fake-useragent'
        ]
        
        missing_deps = []
        for dep in required_deps:
            if dep not in requirements:
                missing_deps.append(dep)
        
        if missing_deps:
            print(f"[符号][符号][符号][符号]: {missing_deps}")
        else:
            print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
    
    def generate_cleanup_report(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("\n" + "="*60)
        print("[符号][符号][符号][符号]")
        print("="*60)
        
        print(f"[符号][符号][符号][符号][符号][符号][符号]: {len(self.cleaned_files)}")
        for file_path in self.cleaned_files:
            print(f"  - {file_path}")
        
        print(f"\n[符号][符号][符号][符号][符号][符号][符号]: {len(self.removed_files)}")
        for file_path in self.removed_files[:10]:  # [符号][符号][符号][符号]10[符号]
            print(f"  - {file_path}")
        if len(self.removed_files) > 10:
            print(f"  ... [符号][符号] {len(self.removed_files) - 10} [符号][符号][符号]")
        
        print(f"\n[符号][符号][符号][符号][符号][符号][符号]: {len(self.optimized_files)}")
        for file_path in self.optimized_files:
            print(f"  - {file_path}")
        
        # [符号][符号][符号][符号][符号][符号]
        report_path = self.project_root / "cleanup_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]\n\n")
            f.write(f"[符号][符号][符号][符号]: {__import__('datetime').datetime.now()}\n\n")
            f.write(f"## [符号][符号][符号][符号]\n\n")
            f.write(f"- [符号][符号][符号][符号][符号]: {len(self.cleaned_files)}\n")
            f.write(f"- [符号][符号][符号][符号][符号]: {len(self.removed_files)}\n") 
            f.write(f"- [符号][符号][符号][符号][符号]: {len(self.optimized_files)}\n\n")
            f.write("## [符号][符号][符号][符号]\n\n")
            f.write("### [符号][符号][符号][符号][符号]\n")
            for file_path in self.cleaned_files:
                f.write(f"- {file_path}\n")
            f.write("\n### [符号][符号][符号][符号][符号]\n")
            for file_path in self.removed_files:
                f.write(f"- {file_path}\n")
            f.write("\n### [符号][符号][符号][符号][符号]\n")
            for file_path in self.optimized_files:
                f.write(f"- {file_path}\n")
        
        print(f"\n[符号][符号][符号][符号][符号][符号][符号][符号]: {report_path}")
    
    def run_cleanup(self):
        """[符号][符号][符号][符号][符号][符号]"""
        print("[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]...")
        print("="*60)
        
        # 1. [符号][符号][符号][符号][符号][符号]
        self.clean_device_profiles()
        print()
        
        # 2. [符号][符号][符号][符号][符号][符号]
        self.remove_redundant_files()
        print()
        
        # 3. [符号][符号]F5 Shape[符号][符号]
        self.optimize_f5_shape_implementation()
        print()
        
        # 4. [符号][符号][符号][符号]
        self.check_f5_dependencies()
        print()
        
        # 5. [符号][符号][符号][符号]
        self.generate_cleanup_report()
        
        print("\n[符号][符号][符号][符号][符号]")

def main():
    """[符号][符号][符号]"""
    project_root = os.getcwd()
    cleaner = StarbucksSystemCleaner(project_root)
    cleaner.run_cleanup()

if __name__ == "__main__":
    main()
