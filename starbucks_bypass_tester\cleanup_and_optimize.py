#!/usr/bin/env python3
"""
星巴克设备指纹绕过系统 - 代码清理和优化工具
清理冗余代码、重复设备、无用文件，优化F5 Shape算法实现
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Set
import re

class StarbucksSystemCleaner:
    """星巴克系统清理器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.device_config_path = self.project_root / "src/config/device_profiles.json"
        self.cleaned_files = []
        self.removed_files = []
        self.optimized_files = []
        
    def clean_device_profiles(self):
        """清理设备配置文件中的重复设备"""
        print("清理设备配置文件...")
        
        if not self.device_config_path.exists():
            print(f"设备配置文件不存在: {self.device_config_path}")
            return
            
        # 备份原文件
        backup_path = self.device_config_path.with_suffix('.json.backup')
        shutil.copy2(self.device_config_path, backup_path)
        print(f"已备份原文件到: {backup_path}")
        
        # 读取配置
        with open(self.device_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查重复设备
        devices = config.get('devices', [])
        seen_device_ids = set()
        unique_devices = []
        duplicates = []
        
        for device in devices:
            device_id = device.get('device_id')
            if device_id in seen_device_ids:
                duplicates.append(device_id)
                print(f"发现重复设备: {device_id}")
            else:
                seen_device_ids.add(device_id)
                unique_devices.append(device)
        
        print(f"原设备数量: {len(devices)}")
        print(f"去重后数量: {len(unique_devices)}")
        print(f"重复设备: {duplicates}")
        
        # 确保有30个设备
        if len(unique_devices) < 30:
            print(f"警告: 去重后只有{len(unique_devices)}个设备，需要30个设备")
            # 保留前30个唯一设备
            unique_devices = unique_devices[:30]
        elif len(unique_devices) > 30:
            # 只保留前30个
            unique_devices = unique_devices[:30]
            print(f"保留前30个设备")
        
        # 更新配置
        config['devices'] = unique_devices
        config['device_pool']['total_devices'] = len(unique_devices)
        
        # 写回文件
        with open(self.device_config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"设备配置文件已清理，保留{len(unique_devices)}个设备")
        self.cleaned_files.append(str(self.device_config_path))
    
    def remove_redundant_files(self):
        """删除冗余文件"""
        print("删除冗余文件...")
        
        # 要删除的文件模式
        redundant_patterns = [
            # 临时文件
            "*.tmp", "*.temp", "*.bak", "*.backup",
            # 编译文件
            "*.pyc", "*.pyo", "*.pyd",
            # 日志文件（保留最新的）
            "*.log.old", "*.log.1", "*.log.2",
            # 测试临时文件
            ".pytest_cache", "__pycache__",
            # 编辑器临时文件
            "*.swp", "*.swo", "*~",
            # 系统文件
            ".DS_Store", "Thumbs.db"
        ]
        
        # 要删除的具体文件
        redundant_files = [
            # 重复的安装问题文档
            "安装问题9.md",
            "安装问题9_更新版.md", 
            "服务器修复指令_问题9.md",
            # 重复的脚本
            "scripts/fix_api_initialization_issue9_complete.sh"
        ]
        
        removed_count = 0
        
        # 删除模式匹配的文件
        for pattern in redundant_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    try:
                        file_path.unlink()
                        self.removed_files.append(str(file_path))
                        removed_count += 1
                        print(f"删除: {file_path}")
                    except Exception as e:
                        print(f"删除失败 {file_path}: {e}")
        
        # 删除具体文件
        for file_name in redundant_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                try:
                    file_path.unlink()
                    self.removed_files.append(str(file_path))
                    removed_count += 1
                    print(f"删除: {file_path}")
                except Exception as e:
                    print(f"删除失败 {file_path}: {e}")
        
        print(f"共删除 {removed_count} 个冗余文件")
    
    def optimize_f5_shape_implementation(self):
        """优化F5 Shape算法实现"""
        print("优化F5 Shape算法实现...")
        
        # 检查F5 Shape相关文件
        f5_files = [
            "src/core/device_fingerprint_engine.py",
            "src/core/header_generator.py",
            "src/core/bypass_engine.py"
        ]
        
        for file_name in f5_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self._optimize_python_file(file_path)
    
    def _optimize_python_file(self, file_path: Path):
        """优化Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_lines = len(content.splitlines())
            
            # 移除多余的空行
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
            
            # 移除行尾空格
            content = re.sub(r'[ \t]+$', '', content, flags=re.MULTILINE)
            
            # 确保文件以换行符结尾
            if not content.endswith('\n'):
                content += '\n'
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            new_lines = len(content.splitlines())
            if original_lines != new_lines:
                print(f"优化 {file_path}: {original_lines} -> {new_lines} 行")
                self.optimized_files.append(str(file_path))
                
        except Exception as e:
            print(f"优化文件失败 {file_path}: {e}")
    
    def check_f5_dependencies(self):
        """检查F5 Shape相关依赖"""
        print("检查F5 Shape相关依赖...")
        
        requirements_path = self.project_root / "requirements.txt"
        if not requirements_path.exists():
            print("requirements.txt 不存在")
            return
        
        with open(requirements_path, 'r', encoding='utf-8') as f:
            requirements = f.read()
        
        # 检查必要的依赖
        required_deps = [
            'requests',
            'aiohttp', 
            'fastapi',
            'cryptography',
            'fake-useragent'
        ]
        
        missing_deps = []
        for dep in required_deps:
            if dep not in requirements:
                missing_deps.append(dep)
        
        if missing_deps:
            print(f"缺少依赖: {missing_deps}")
        else:
            print("所有必要依赖都已安装")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        print("\n" + "="*60)
        print("清理报告")
        print("="*60)
        
        print(f"清理的文件数量: {len(self.cleaned_files)}")
        for file_path in self.cleaned_files:
            print(f"  - {file_path}")
        
        print(f"\n删除的文件数量: {len(self.removed_files)}")
        for file_path in self.removed_files[:10]:  # 只显示前10个
            print(f"  - {file_path}")
        if len(self.removed_files) > 10:
            print(f"  ... 还有 {len(self.removed_files) - 10} 个文件")
        
        print(f"\n优化的文件数量: {len(self.optimized_files)}")
        for file_path in self.optimized_files:
            print(f"  - {file_path}")
        
        # 生成报告文件
        report_path = self.project_root / "cleanup_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 星巴克设备指纹绕过系统清理报告\n\n")
            f.write(f"清理时间: {__import__('datetime').datetime.now()}\n\n")
            f.write(f"## 清理统计\n\n")
            f.write(f"- 清理的文件: {len(self.cleaned_files)}\n")
            f.write(f"- 删除的文件: {len(self.removed_files)}\n") 
            f.write(f"- 优化的文件: {len(self.optimized_files)}\n\n")
            f.write("## 详细列表\n\n")
            f.write("### 清理的文件\n")
            for file_path in self.cleaned_files:
                f.write(f"- {file_path}\n")
            f.write("\n### 删除的文件\n")
            for file_path in self.removed_files:
                f.write(f"- {file_path}\n")
            f.write("\n### 优化的文件\n")
            for file_path in self.optimized_files:
                f.write(f"- {file_path}\n")
        
        print(f"\n清理报告已保存到: {report_path}")
    
    def run_cleanup(self):
        """执行完整清理"""
        print("开始清理星巴克设备指纹绕过系统...")
        print("="*60)
        
        # 1. 清理设备配置
        self.clean_device_profiles()
        print()
        
        # 2. 删除冗余文件
        self.remove_redundant_files()
        print()
        
        # 3. 优化F5 Shape实现
        self.optimize_f5_shape_implementation()
        print()
        
        # 4. 检查依赖
        self.check_f5_dependencies()
        print()
        
        # 5. 生成报告
        self.generate_cleanup_report()
        
        print("\n清理完成！")

def main():
    """主函数"""
    project_root = os.getcwd()
    cleaner = StarbucksSystemCleaner(project_root)
    cleaner.run_cleanup()

if __name__ == "__main__":
    main()
