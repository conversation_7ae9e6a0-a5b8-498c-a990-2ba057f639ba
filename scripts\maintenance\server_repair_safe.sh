#!/bin/bash

# 安全的服务器修复脚本
# 使用Python脚本进行精确修复，避免sed重复替换问题

echo "🔧 开始安全修复服务器代码..."

# 1. 备份原文件
echo "📦 备份原文件..."
cp src/core/bypass_engine.py src/core/bypass_engine.py.backup.$(date +%Y%m%d_%H%M%S)
cp src/core/api_service.py src/core/api_service.py.backup.$(date +%Y%m%d_%H%M%S)
cp src/core/device_fingerprint_engine.py src/core/device_fingerprint_engine.py.backup.$(date +%Y%m%d_%H%M%S)

# 2. 创建Python修复脚本
cat > temp_fix_script.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def fix_bypass_engine():
    """修复bypass_engine.py的类型注解"""
    file_path = 'src/core/bypass_engine.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: DeviceFingerprintEngine类型注解
    content = re.sub(
        r'fingerprint_engine: DeviceFingerprintEngine = None',
        "fingerprint_engine: 'DeviceFingerprintEngine' = None",
        content
    )
    
    # 修复2: DeviceFingerprint类型注解
    content = re.sub(
        r'fingerprint: DeviceFingerprint\) -> float:',
        "fingerprint: 'DeviceFingerprint') -> float:",
        content
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ bypass_engine.py 类型注解修复完成")

def fix_api_service():
    """修复api_service.py的方法调用和返回值访问"""
    file_path = 'src/core/api_service.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: 方法名
    content = re.sub(
        r'self\.bypass_engine\.get_bypass_stats\(\)',
        'await self.bypass_engine.get_bypass_statistics()',
        content
    )
    
    # 修复2: 参数传递 - 只修复没有data=的行
    content = re.sub(
        r'(\s+)(request\.data,)(\s*\n\s*strategy\))',
        r'\1data=request.data,\3',
        content
    )
    content = re.sub(
        r'(\s+)(bypass_req\.data,)(\s*\n\s*strategy=strategy)',
        r'\1data=bypass_req.data,\3',
        content
    )
    
    # 修复3: 返回值访问 - 只替换没有被替换过的
    replacements = [
        (r"result\.success(?!\[)", "result['success']"),
        (r"result\.risk_level\.value", "result['risk_level']"),
        (r"result\.confidence(?!\[)", "result['confidence']"),
        (r"result\.fingerprint_quality(?!\[)", "result['fingerprint_quality']"),
        (r"result\.bypass_techniques(?!\[)", "result['bypass_techniques']"),
        (r"result\.warnings(?!\[)", "result['warnings']"),
        (r"result\.execution_time(?!\[)", "result['execution_time']"),
        (r"result\.timestamp\.isoformat\(\)", "result['timestamp']"),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ api_service.py 方法调用修复完成")

def fix_device_fingerprint():
    """修复device_fingerprint_engine.py添加缺失方法"""
    file_path = 'src/core/device_fingerprint_engine.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经有success_rate字段
    if 'success_rate: float = 1.0' not in content:
        # 添加success_rate字段
        content = re.sub(
            r'(additional_headers: Optional\[Dict\[str, str\]\] = None.*?# 添加additional_headers参数以兼容测试)',
            r'\1\n    success_rate: float = 1.0  # 添加成功率字段',
            content
        )
    
    # 检查是否已经有is_available方法
    if 'def is_available(self) -> bool:' not in content:
        # 添加方法
        methods_code = '''
    
    def is_available(self) -> bool:
        """检查设备指纹是否可用"""
        if not self.is_active:
            return False
        
        # 检查冷却时间
        if self.last_used:
            from datetime import timedelta
            cooldown_minutes = 5  # 默认冷却时间5分钟
            cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
            if datetime.now() < cooldown_end:
                return False
        
        return True
    
    def get_cooldown_remaining(self) -> float:
        """获取剩余冷却时间（分钟）"""
        if not self.last_used:
            return 0.0
        
        from datetime import timedelta
        cooldown_minutes = 5  # 默认冷却时间5分钟
        cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
        now = datetime.now()
        
        if now >= cooldown_end:
            return 0.0
        
        remaining = cooldown_end - now
        return remaining.total_seconds() / 60.0  # 转换为分钟'''
        
        # 在success_rate字段后添加方法
        content = re.sub(
            r'(success_rate: float = 1\.0  # 添加成功率字段)',
            r'\1' + methods_code,
            content
        )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ device_fingerprint_engine.py 方法添加完成")

def main():
    print("🔧 开始修复代码...")
    
    try:
        fix_bypass_engine()
        fix_api_service()
        fix_device_fingerprint()
        print("🎉 所有修复完成！")
        return True
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
EOF

# 3. 运行修复脚本
echo "🔧 运行Python修复脚本..."
python3 temp_fix_script.py

# 4. 验证语法
echo "🔍 验证语法..."
python3 -m py_compile src/core/bypass_engine.py
python3 -m py_compile src/core/api_service.py  
python3 -m py_compile src/core/device_fingerprint_engine.py

if [ $? -eq 0 ]; then
    echo "✅ 语法检查通过"
else
    echo "❌ 语法检查失败，请检查代码"
    exit 1
fi

# 5. 清理临时文件
rm -f temp_fix_script.py

echo "✅ 安全修复完成！"
