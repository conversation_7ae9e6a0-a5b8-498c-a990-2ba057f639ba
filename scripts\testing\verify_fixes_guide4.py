#!/usr/bin/env python3
"""
[符号][符号][符号][符号][符号][符号]4[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]
Verify that all fixes from Guide 4 are correctly applied
"""

import os
import sys
import ast
import re
from pathlib import Path

def check_file_syntax(file_path):
    """[符号][符号]Python[符号][符号][符号][符号]"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, "[符号][符号][符号][符号]"
    except SyntaxError as e:
        return False, f"[符号][符号][符号][符号]: {e}"
    except Exception as e:
        return False, f"[符号][符号][符号][符号]: {e}"

def check_import_pattern(file_path, pattern_name, expected_pattern):
    """[符号][符号][符号][符号][符号][符号]"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if expected_pattern in content:
            return True, f"{pattern_name} [符号][符号][符号][符号][符号][符号]"
        else:
            return False, f"{pattern_name} [符号][符号][符号][符号][符号][符号][符号]"
    except Exception as e:
        return False, f"[符号][符号][符号][符号]: {e}"

def check_class_definition(file_path, class_name):
    """[符号][符号][符号][符号][符号][符号][符号][符号][符号]"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        pattern = f"class {class_name}"
        if pattern in content:
            return True, f"{class_name} [符号][符号][符号][符号][符号]"
        else:
            return False, f"{class_name} [符号][符号][符号][符号][符号]"
    except Exception as e:
        return False, f"[符号][符号][符号][符号]: {e}"

def main():
    """[符号][符号][符号][符号][符号]"""
    print("[[符号][符号]] [符号][符号][符号][符号][符号][符号]4[符号][符号][符号][符号][符号]...")
    print("=" * 60)
    
    # [符号][符号][符号][符号][符号][符号][符号]
    project_root = Path(__file__).parent.parent / "starbucks_bypass_tester"
    if not project_root.exists():
        project_root = Path(__file__).parent.parent
    
    os.chdir(project_root)
    
    all_passed = True
    
    # 1. [符号][符号] device_fingerprint_engine.py
    print("\n1. [符号][符号] device_fingerprint_engine.py")
    print("-" * 40)
    
    file_path = "src/core/device_fingerprint_engine.py"
    
    # [符号][符号][符号][符号]
    passed, msg = check_file_syntax(file_path)
    print(f"   [符号][符号][符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # [符号][符号][符号][符号][符号][符号]
    expected_import = """try:
    from ..utils.logger import get_logger
    from ..config.config_manager import ConfigManager
except ImportError:
    # [符号][符号][符号][符号][符号][符号][符号][符号]
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from utils.logger import get_logger
    from config.config_manager import ConfigManager"""
    
    passed, msg = check_import_pattern(file_path, "try-except[符号][符号]", expected_import)
    print(f"   [符号][符号][符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # 2. [符号][符号] concurrency_controller.py
    print("\n2. [符号][符号] concurrency_controller.py")
    print("-" * 40)
    
    file_path = "src/core/concurrency_controller.py"
    
    # [符号][符号][符号][符号]
    passed, msg = check_file_syntax(file_path)
    print(f"   [符号][符号][符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # LoadBalanceStrategy [符号][符号][符号]
    passed, msg = check_class_definition(file_path, "LoadBalanceStrategy")
    print(f"   LoadBalanceStrategy[符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # [符号][符号][符号][符号][符号][符号]
    expected_import = """try:
    from ..utils.logger import get_logger
    from ..config.config_manager import ConfigManager
except ImportError:
    # [符号][符号][符号][符号][符号][符号][符号][符号]
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from utils.logger import get_logger
    from config.config_manager import ConfigManager"""
    
    passed, msg = check_import_pattern(file_path, "try-except[符号][符号]", expected_import)
    print(f"   [符号][符号][符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # 3. [符号][符号] api_service.py
    print("\n3. [符号][符号] api_service.py")
    print("-" * 40)
    
    file_path = "src/core/api_service.py"
    
    # [符号][符号][符号][符号]
    passed, msg = check_file_syntax(file_path)
    print(f"   [符号][符号][符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # AntiDetectionEngine [符号][符号][符号][符号]
    expected_import = "from .bypass_engine import AntiDetectionEngine"
    passed, msg = check_import_pattern(file_path, "AntiDetectionEngine[符号][符号]", expected_import)
    print(f"   AntiDetectionEngine[符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # 4. [符号][符号] cli/main.py
    print("\n4. [符号][符号] cli/main.py")
    print("-" * 40)
    
    file_path = "src/cli/main.py"
    
    # [符号][符号][符号][符号]
    passed, msg = check_file_syntax(file_path)
    print(f"   [符号][符号][符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # AntiDetectionEngine [符号][符号][符号][符号]
    expected_import = "from core.bypass_engine import BypassEngine, AntiDetectionEngine"
    passed, msg = check_import_pattern(file_path, "AntiDetectionEngine[符号][符号]", expected_import)
    print(f"   AntiDetectionEngine[符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # 5. [符号][符号] bypass_engine.py
    print("\n5. [符号][符号] bypass_engine.py")
    print("-" * 40)
    
    file_path = "src/core/bypass_engine.py"
    
    # [符号][符号][符号][符号]
    passed, msg = check_file_syntax(file_path)
    print(f"   [符号][符号][符号][符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # AntiDetectionEngine [符号][符号][符号]
    passed, msg = check_class_definition(file_path, "AntiDetectionEngine")
    print(f"   AntiDetectionEngine[符号]: {'[[符号][符号]]' if passed else '[[符号][符号]]'} {msg}")
    if not passed:
        all_passed = False
    
    # 6. [符号][符号][符号][符号][符号][符号]
    print("\n6. [符号][符号][符号][符号][符号][符号]")
    print("-" * 40)
    
    sys.path.insert(0, str(Path(project_root) / "src"))
    
    try:
        from core.device_fingerprint_engine import DeviceFingerprintEngine
        print("   [[符号][符号]] DeviceFingerprintEngine [符号][符号][符号][符号]")
    except Exception as e:
        print(f"   [[符号][符号]] DeviceFingerprintEngine [符号][符号][符号][符号]: {e}")
        all_passed = False
    
    try:
        from core.concurrency_controller import ConcurrencyController, LoadBalanceStrategy
        print("   [[符号][符号]] ConcurrencyController [符号] LoadBalanceStrategy [符号][符号][符号][符号]")
    except Exception as e:
        print(f"   [[符号][符号]] ConcurrencyController [符号][符号][符号][符号]: {e}")
        all_passed = False
    
    try:
        from core.bypass_engine import BypassEngine, AntiDetectionEngine
        print("   [[符号][符号]] BypassEngine [符号] AntiDetectionEngine [符号][符号][符号][符号]")
    except Exception as e:
        print(f"   [[符号][符号]] BypassEngine [符号][符号][符号][符号]: {e}")
        all_passed = False
    
    try:
        from core.api_service import APIService
        print("   [[符号][符号]] APIService [符号][符号][符号][符号]")
    except Exception as e:
        if "pydantic_core" in str(e):
            print(f"   [[符号][符号]]  APIService [符号][符号][符号][符号]([符号][符号][符号][符号]pydantic[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]): {e}")
        else:
            print(f"   [[符号][符号]] APIService [符号][符号][符号][符号]: {e}")
            all_passed = False

    try:
        from cli.main import StarBucksCLI
        print("   [[符号][符号]] StarBucksCLI [符号][符号][符号][符号]")
    except Exception as e:
        if "pydantic_core" in str(e):
            print(f"   [[符号][符号]]  StarBucksCLI [符号][符号][符号][符号]([符号][符号][符号][符号]pydantic[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]): {e}")
        else:
            print(f"   [[符号][符号]] StarBucksCLI [符号][符号][符号][符号]: {e}")
            all_passed = False
    
    # [符号][符号]
    print("\n" + "=" * 60)
    if all_passed:
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]4[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号]4[符号][符号][符号][符号]")
        return 0
    else:
        print("[[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
        return 1

if __name__ == "__main__":
    exit(main())
