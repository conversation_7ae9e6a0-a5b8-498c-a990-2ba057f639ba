#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终验证脚本
验证API模型修复是否完整且正确
"""

import sys
import os
import re
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent / "starbucks_bypass_tester"
sys.path.insert(0, str(project_root / "src"))

def verify_api_service_file():
    """验证API服务文件"""
    print("🔍 验证API服务文件...")
    
    api_file = project_root / "src" / "core" / "api_service.py"
    
    if not api_file.exists():
        print(f"❌ API服务文件不存在: {api_file}")
        return False
    
    with open(api_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查必需的模式
    checks = [
        {
            'name': 'BypassResponse模型包含strategy_used字段',
            'pattern': r'class BypassResponse.*?strategy_used: Optional\[str\] = None',
            'required': True
        },
        {
            'name': 'BypassResponse模型包含modified_fingerprint字段',
            'pattern': r'class BypassResponse.*?modified_fingerprint: Optional\[Dict\[str, Any\]\] = None',
            'required': True
        },
        {
            'name': '单次绕过响应构建包含strategy_used',
            'pattern': r'strategy_used=result\.get\(\'strategy_used\'\)',
            'required': True
        },
        {
            'name': '单次绕过响应构建包含modified_fingerprint',
            'pattern': r'modified_fingerprint=None.*?# 不返回敏感的指纹信息',
            'required': True
        },
        {
            'name': '批量绕过成功响应包含strategy_used',
            'pattern': r'strategy_used=result\.get\(\'strategy_used\'\)',
            'required': True,
            'count': 2  # 应该出现2次
        },
        {
            'name': '批量绕过错误响应包含strategy_used=None',
            'pattern': r'strategy_used=None',
            'required': True
        }
    ]
    
    all_passed = True
    
    for check in checks:
        pattern = check['pattern']
        matches = re.findall(pattern, content, re.DOTALL)
        
        if check['required']:
            expected_count = check.get('count', 1)
            if len(matches) >= expected_count:
                print(f"  ✅ {check['name']}")
            else:
                print(f"  ❌ {check['name']} (找到 {len(matches)} 个，期望 {expected_count} 个)")
                all_passed = False
        else:
            if matches:
                print(f"  ✅ {check['name']}")
            else:
                print(f"  ❌ {check['name']}")
                all_passed = False
    
    return all_passed

def verify_syntax():
    """验证语法"""
    print("🔍 验证语法...")
    
    api_file = project_root / "src" / "core" / "api_service.py"
    
    try:
        with open(api_file, 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, str(api_file), 'exec')
        print("  ✅ 语法验证通过")
        return True
        
    except SyntaxError as e:
        print(f"  ❌ 语法错误: {e}")
        print(f"     行号: {e.lineno}")
        print(f"     位置: {e.offset}")
        return False
    except Exception as e:
        print(f"  ❌ 验证失败: {e}")
        return False

def verify_imports():
    """验证导入"""
    print("🔍 验证导入...")
    
    try:
        # 切换到项目目录
        os.chdir(project_root)
        
        # 尝试导入关键模块
        from core.api_service import BypassResponse, BypassRequest
        print("  ✅ API模型导入成功")
        
        # 检查BypassResponse字段
        response_fields = BypassResponse.__fields__.keys()
        required_fields = [
            'success', 'request_id', 'risk_level', 'confidence',
            'fingerprint_quality', 'bypass_techniques', 'warnings',
            'execution_time', 'timestamp', 'strategy_used', 
            'modified_fingerprint', 'response_data'
        ]
        
        missing_fields = [field for field in required_fields if field not in response_fields]
        
        if missing_fields:
            print(f"  ❌ BypassResponse缺失字段: {missing_fields}")
            return False
        else:
            print("  ✅ BypassResponse包含所有必需字段")
            return True
            
    except ImportError as e:
        print(f"  ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 验证失败: {e}")
        return False

def create_test_response():
    """创建测试响应"""
    print("🔍 创建测试响应...")
    
    try:
        os.chdir(project_root)
        from core.api_service import BypassResponse
        
        # 测试数据
        test_data = {
            "success": True,
            "request_id": "test_123",
            "risk_level": "low",
            "confidence": 0.8,
            "fingerprint_quality": 0.9,
            "bypass_techniques": ["header_randomization"],
            "warnings": [],
            "execution_time": 0.5,
            "timestamp": "2025-07-31T15:30:00",
            "strategy_used": "adaptive",
            "modified_fingerprint": None,
            "response_data": None
        }
        
        # 创建响应对象
        response = BypassResponse(**test_data)
        print("  ✅ 测试响应创建成功")
        
        # 验证字段访问
        assert response.strategy_used == "adaptive"
        assert response.modified_fingerprint is None
        print("  ✅ 新字段访问正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试响应创建失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("最终验证 - API模型修复")
    print("=" * 60)
    
    success = True
    
    # 1. 验证语法
    if not verify_syntax():
        success = False
    
    # 2. 验证文件内容
    if not verify_api_service_file():
        success = False
    
    # 3. 验证导入
    if not verify_imports():
        success = False
    
    # 4. 创建测试响应
    if not create_test_response():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有验证通过 - API模型修复完全正确!")
        print("✅ 可以安全部署到服务器")
        print("✅ 单次绕过API应该能正常工作")
        print("✅ 所有API测试应该100%通过")
    else:
        print("❌ 验证失败 - 需要进一步修复")
        print("⚠️ 请检查上述错误并修复")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
