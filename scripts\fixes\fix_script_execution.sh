#!/bin/bash

# 修复脚本执行问题
# 解决 "cannot execute: required file not found" 错误

echo "=== 修复脚本执行问题 ==="

# 1. 检查当前目录
echo "当前目录: $(pwd)"
echo "目录内容:"
ls -la

echo ""
echo "=== 修复步骤 ==="

# 2. 修复换行符问题 (Windows -> Linux)
echo "1. 修复换行符格式..."
if command -v dos2unix >/dev/null 2>&1; then
    find . -name "*.sh" -exec dos2unix {} \;
    echo "   使用dos2unix修复完成"
else
    # 使用sed替代dos2unix
    find . -name "*.sh" -exec sed -i 's/\r$//' {} \;
    echo "   使用sed修复完成"
fi

# 3. 设置执行权限
echo "2. 设置执行权限..."
find . -name "*.sh" -exec chmod +x {} \;
echo "   权限设置完成"

# 4. 验证bash解释器
echo "3. 验证bash解释器..."
which bash
echo "   bash路径: $(which bash)"

# 5. 检查脚本头部
echo "4. 检查脚本头部..."
for script in *.sh; do
    if [ -f "$script" ]; then
        echo "   $script: $(head -1 "$script")"
    fi
done

# 6. 测试脚本执行
echo "5. 测试脚本执行..."
if [ -f "create_user.sh" ]; then
    echo "   测试create_user.sh..."
    if bash -n create_user.sh; then
        echo "   ✅ create_user.sh 语法检查通过"
    else
        echo "   ❌ create_user.sh 语法检查失败"
    fi
    
    echo "   文件信息:"
    file create_user.sh
    echo "   权限信息:"
    ls -la create_user.sh
else
    echo "   ❌ create_user.sh 文件不存在"
fi

echo ""
echo "=== 修复完成 ==="
echo "现在可以尝试执行: ./create_user.sh"
