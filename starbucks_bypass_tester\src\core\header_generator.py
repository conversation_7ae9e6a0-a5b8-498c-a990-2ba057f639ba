"""
请求头生成器
负责生成完整的HTTP请求头，包括固定字段和动态字段
"""

import json
import base64
import random
import time
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import secrets
import string

from ..utils.logger import get_logger

class HeaderGenerator:
    """HTTP请求头生成器"""

    def __init__(self, fixed_fields_file: str = None, dynamic_analysis_file: str = None):
        """
        初始化请求头生成器

        Args:
            fixed_fields_file: 固定字段配置文件路径
            dynamic_analysis_file: 动态字段分析文件路径
        """
        self.fixed_fields = {}
        self.dynamic_patterns = {}
        self.dynamic_analysis = {}  # 兼容测试接口
        self.logger = get_logger(self.__class__.__name__)

        # 统计信息
        self.generation_count = 0
        self.field_usage_stats = {}

        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        }

        if fixed_fields_file:
            self.load_fixed_fields(fixed_fields_file)
        if dynamic_analysis_file:
            self.load_dynamic_patterns(dynamic_analysis_file)

    def load_fixed_fields(self, file_path_or_dict) -> bool:
        """
        加载固定字段配置（兼容文件路径和字典）

        Args:
            file_path_or_dict: 固定字段文件路径或字典

        Returns:
            bool: 加载是否成功
        """
        try:
            if isinstance(file_path_or_dict, dict):
                # 直接使用字典
                self.fixed_fields = file_path_or_dict
                self.logger.info(f"从字典加载了 {len(self.fixed_fields)} 个固定字段")
            else:
                # 从文件加载
                with open(file_path_or_dict, 'r', encoding='utf-8') as f:
                    self.fixed_fields = json.load(f)
                self.logger.info(f"从文件加载了 {len(self.fixed_fields)} 个固定字段")
            return True
        except Exception as e:
            self.logger.error(f"加载固定字段失败: {e}")
            return False

    def load_dynamic_patterns(self, file_path: str) -> bool:
        """
        加载动态字段模式

        Args:
            file_path: 动态字段分析文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.dynamic_patterns = json.load(f)
            print(f"加载了 {len(self.dynamic_patterns)} 个动态字段模式")
            return True
        except Exception as e:
            print(f"加载动态字段模式失败: {e}")
            return False

    def load_fixed_fields_from_dict(self, fixed_fields_dict: Dict) -> bool:
        """
        从字典加载固定字段

        Args:
            fixed_fields_dict: 固定字段字典

        Returns:
            bool: 加载是否成功
        """
        try:
            self.fixed_fields = fixed_fields_dict
            print(f"从字典加载了 {len(self.fixed_fields)} 个固定字段")
            return True
        except Exception as e:
            print(f"从字典加载固定字段失败: {e}")
            return False

    def load_dynamic_patterns_from_dict(self, dynamic_analysis_dict: Dict) -> bool:
        """
        从字典加载动态字段模式

        Args:
            dynamic_analysis_dict: 动态字段分析字典

        Returns:
            bool: 加载是否成功
        """
        try:
            self.dynamic_patterns = dynamic_analysis_dict
            print(f"从字典加载了 {len(self.dynamic_patterns)} 个动态字段模式")
            return True
        except Exception as e:
            print(f"从字典加载动态字段模式失败: {e}")
            return False

    def load_dynamic_analysis(self, dynamic_analysis_dict: Dict) -> bool:
        """
        加载动态字段分析（兼容测试接口）

        Args:
            dynamic_analysis_dict: 动态字段分析字典

        Returns:
            bool: 加载是否成功
        """
        try:
            self.dynamic_analysis = dynamic_analysis_dict
            # 同时更新dynamic_patterns以保持兼容性
            self.dynamic_patterns = dynamic_analysis_dict
            self.logger.info(f"加载了 {len(self.dynamic_analysis)} 个动态字段分析")
            return True
        except Exception as e:
            self.logger.error(f"加载动态字段分析失败: {e}")
            return False

    def update_dynamic_field_config(self, new_config: Dict) -> bool:
        """
        更新动态字段配置（兼容测试接口）

        Args:
            new_config: 新的配置字典

        Returns:
            bool: 更新是否成功
        """
        try:
            if not hasattr(self, 'dynamic_analysis'):
                self.dynamic_analysis = {}

            self.dynamic_analysis.update(new_config)
            # 同时更新dynamic_patterns以保持兼容性
            self.dynamic_patterns.update(new_config)

            self.logger.info(f"更新了 {len(new_config)} 个动态字段配置")
            return True
        except Exception as e:
            self.logger.error(f"更新动态字段配置失败: {e}")
            return False

    def get_field_statistics(self) -> Dict[str, Any]:
        """
        获取字段统计信息（兼容测试接口）

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            return {
                'fixed_fields_count': len(self.fixed_fields),
                'dynamic_fields_count': len(getattr(self, 'dynamic_analysis', {})),
                'generation_count': getattr(self, 'generation_count', 0),
                'field_usage_stats': getattr(self, 'field_usage_stats', {}),
                'total_fields': len(self.fixed_fields) + len(getattr(self, 'dynamic_analysis', {}))
            }
        except Exception as e:
            self.logger.error(f"获取字段统计信息失败: {e}")
            return {}

    def reset_statistics(self) -> bool:
        """
        重置统计信息（兼容测试接口）

        Returns:
            bool: 重置是否成功
        """
        try:
            self.generation_count = 0
            self.field_usage_stats = {}
            self.logger.info("统计信息已重置")
            return True
        except Exception as e:
            self.logger.error(f"重置统计信息失败: {e}")
            return False

    def generate_xhpacpxq_e_field(self) -> str:
        """
        生成X-XHPAcPXq-e字段值
        这是最关键的动态字段，每次请求都必须变化

        Returns:
            str: 生成的字段值
        """
        # 基于时间戳和随机数生成动态内容
        timestamp = int(time.time() * 1000)  # 毫秒时间戳
        random_bytes = secrets.token_bytes(32)  # 32字节随机数

        # 创建一个复合数据结构
        data_parts = [
            b"b;",  # 固定前缀（从样本数据观察到）
            str(timestamp).encode(),
            random_bytes,
            secrets.token_bytes(16)  # 额外的随机数据
        ]

        # 组合数据
        combined_data = b"".join(data_parts)

        # 进行Base64编码
        encoded = base64.b64encode(combined_data).decode()

        # 添加分隔符和额外的Base64段（模拟观察到的模式）
        additional_segment = base64.b64encode(secrets.token_bytes(24)).decode()

        return f"{encoded};{additional_segment}"

    def generate_time_field(self) -> str:
        """
        生成时间字段

        Returns:
            str: 格式化的时间字符串
        """
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def select_from_samples(self, field_name: str) -> Optional[str]:
        """
        从样本值中选择一个值

        Args:
            field_name: 字段名称

        Returns:
            Optional[str]: 选中的值
        """
        if field_name not in self.dynamic_patterns:
            return None

        pattern = self.dynamic_patterns[field_name]

        # 处理两种数据格式：直接列表或包含sample_values的字典
        if isinstance(pattern, list):
            sample_values = pattern
        elif isinstance(pattern, dict):
            sample_values = pattern.get('sample_values', [])
        else:
            return None

        if not sample_values:
            return None

        return random.choice(sample_values)

    def generate_dynamic_field(self, field_name: str) -> Optional[str]:
        """
        生成动态字段值

        Args:
            field_name: 字段名称

        Returns:
            Optional[str]: 生成的字段值
        """
        if field_name == 'X-XHPAcPXq-e':
            return self.generate_xhpacpxq_e_field()
        elif field_name == 'time':
            return self.generate_time_field()
        elif field_name == 'X-XHPAcPXq-a':
            # 时间戳字段
            return str(int(time.time()))
        elif field_name == 'X-XHPAcPXq-b':
            # 随机十六进制字符串
            import secrets
            return secrets.token_hex(16)  # 32字符的十六进制字符串
        else:
            # 对于其他动态字段，从样本中选择
            return self.select_from_samples(field_name)

    def generate_headers(self, device_headers: Dict[str, str] = None,
                        custom_headers: Dict[str, str] = None,
                        custom_fields: Dict[str, str] = None) -> Dict[str, str]:
        """
        生成完整的HTTP请求头

        Args:
            device_headers: 设备相关的请求头
            custom_headers: 自定义请求头

        Returns:
            Dict[str, str]: 完整的请求头字典
        """
        headers = {}

        # 1. 添加基础请求头
        headers.update(self.base_headers)

        # 2. 添加固定字段
        headers.update(self.fixed_fields)

        # 3. 添加设备相关字段
        if device_headers:
            # 如果device_headers是DeviceProfile对象，转换为字典
            if hasattr(device_headers, '__dict__'):
                device_dict = device_headers.__dict__
            elif hasattr(device_headers, 'items'):
                device_dict = device_headers
            else:
                device_dict = {}

            # 处理字段映射和大小写问题
            for key, value in device_dict.items():
                if key.lower() == 'authorization':
                    headers['Authorization'] = value  # 确保Authorization使用正确的大小写
                elif key == 'device_id':
                    headers['x-device-id'] = value  # 映射设备ID
                elif key == 'bs_device_id':
                    headers['x-bs-device-id'] = value  # 映射BS设备ID
                else:
                    headers[key] = value

        # 4. 生成动态字段
        dynamic_fields = [
            'X-XHPAcPXq-e',  # 最重要的动态字段
            'X-XHPAcPXq-a',  # 时间戳字段
            'X-XHPAcPXq-b',  # 随机十六进制字段
            'time'           # 时间戳字段
        ]

        for field in dynamic_fields:
            value = self.generate_dynamic_field(field)
            if value:
                headers[field] = value

        # 5. 从样本中选择其他X-XHPAcPXq系列字段
        xhpacpxq_fields = [
            'X-XHPAcPXq-g', 'X-XHPAcPXq-d', 'X-XHPAcPXq-f',
            'X-XHPAcPXq-c'
        ]

        for field in xhpacpxq_fields:
            if field not in headers:  # 避免覆盖已有字段
                value = self.select_from_samples(field)
                if value:
                    headers[field] = value

        # 6. 添加自定义请求头
        if custom_headers:
            headers.update(custom_headers)

        # 7. 添加自定义字段（兼容测试）
        if custom_fields:
            headers.update(custom_fields)

        # 7. 更新统计信息
        self.generation_count += 1
        for field_name in headers.keys():
            if field_name not in self.field_usage_stats:
                self.field_usage_stats[field_name] = 0
            self.field_usage_stats[field_name] += 1

        return headers

    def generate_random_field(self, field_name: str) -> str:
        """
        生成随机字段（兼容测试）

        Args:
            field_name: 字段名

        Returns:
            str: 随机生成的字段值
        """
        result = self.generate_dynamic_field(field_name)
        if result is None:
            # 如果没有找到动态字段配置，返回默认值
            return f"random_value_{random.randint(1000, 9999)}"
        return result

    def generate_timestamp_field(self, field_name: str = None) -> str:
        """
        生成时间戳字段（兼容测试）

        Args:
            field_name: 字段名称（可选，用于兼容测试）

        Returns:
            str: 时间戳字段值
        """
        import time
        return str(int(time.time()))

    def generate_user_agent(self) -> str:
        """
        生成User-Agent（兼容测试）

        Returns:
            str: User-Agent字符串
        """
        return self.base_headers.get('User-Agent', 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148')

    def generate_signature(self, headers: Dict[str, str]) -> str:
        """
        生成请求签名（兼容测试）

        Args:
            headers: 请求头字典

        Returns:
            str: 生成的签名
        """
        import hashlib
        import json

        # 对请求头进行排序并序列化
        sorted_headers = json.dumps(headers, sort_keys=True)

        # 生成MD5哈希（32字符）
        hash_obj = hashlib.md5(sorted_headers.encode('utf-8'))
        signature = hash_obj.hexdigest()

        return signature

    def validate_headers(self, headers: Dict[str, str]) -> Tuple[bool, List[str]]:
        """
        验证请求头的完整性（兼容测试接口）

        Args:
            headers: 请求头字典

        Returns:
            Tuple[bool, List[str]]: (验证是否通过, 错误列表)
        """
        required_fields = [
            'x-device-id',
            'x-bs-device-id',
            'Authorization',
            'X-XHPAcPXq-z',
            'X-XHPAcPXq-e',
            'time'
        ]

        errors = []
        for field in required_fields:
            if field not in headers:
                errors.append(f"缺少必需字段: {field}")

        # 检查字段值的有效性
        for field, value in headers.items():
            if not value or not isinstance(value, str):
                errors.append(f"字段 {field} 的值无效")

        is_valid = len(errors) == 0

        if not is_valid:
            self.logger.warning(f"请求头验证失败: {errors}")

        return is_valid, errors

    def generate_request_signature(self, headers: Dict[str, str],
                                 method: str = "POST",
                                 url: str = "",
                                 body: str = "") -> str:
        """
        生成请求签名（如果需要）

        Args:
            headers: 请求头
            method: HTTP方法
            url: 请求URL
            body: 请求体

        Returns:
            str: 生成的签名
        """
        # 创建签名字符串
        signature_parts = [
            method.upper(),
            url,
            headers.get('time', ''),
            headers.get('x-device-id', ''),
            body
        ]

        signature_string = '|'.join(signature_parts)

        # 生成MD5哈希
        signature = hashlib.md5(signature_string.encode()).hexdigest()

        return signature

    def create_request_package(self, device_headers: Dict[str, str],
                             method: str = "POST",
                             url: str = "",
                             body: Dict = None,
                             custom_headers: Dict[str, str] = None) -> Dict[str, Any]:
        """
        创建完整的请求包

        Args:
            device_headers: 设备请求头
            method: HTTP方法
            url: 请求URL
            body: 请求体数据
            custom_headers: 自定义请求头

        Returns:
            Dict[str, Any]: 完整的请求包
        """
        # 生成请求头
        headers = self.generate_headers(device_headers, custom_headers)

        # 验证请求头
        if not self.validate_headers(headers):
            raise ValueError("请求头验证失败")

        # 准备请求体
        if body is None:
            body = {}

        body_str = json.dumps(body, separators=(',', ':'))

        # 生成签名（如果需要）
        signature = self.generate_request_signature(headers, method, url, body_str)

        return {
            'method': method,
            'url': url,
            'headers': headers,
            'body': body,
            'signature': signature,
            'timestamp': headers.get('time')
        }

if __name__ == "__main__":
    # 测试代码
    import os
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    fixed_fields_file = os.path.join(base_dir, "data", "processed", "fixed_fields.json")
    dynamic_analysis_file = os.path.join(base_dir, "data", "processed", "dynamic_fields_analysis.json")

    generator = HeaderGenerator(fixed_fields_file, dynamic_analysis_file)

    # 模拟设备请求头
    device_headers = {
        'x-device-id': 'B434ED82-107C-483B-B96F-8BE7DFE55B30',
        'x-bs-device-id': 'h17DIYKSlJnQpcJUA4MJsL5iveyWWoVCQotnbzbDwmnUrFTTGRb_WeDqHzmmKA2Di7H2NnXQSyTjpC9wbtKa2r2IxyvlYsOaG8KqhvX0ses4s3QxCqoZRkDhjv-R0L5Gj7cdELrdM5SFDZP1gQNuLptl5PW0rZH3',
        'Authorization': 'cbf5f1e57fd2499e8c676ea42e73ce04'
    }

    # 生成请求头
    headers = generator.generate_headers(device_headers)

    print("生成的请求头:")
    for key, value in headers.items():
        if len(str(value)) > 50:
            print(f"{key}: {str(value)[:50]}...")
        else:
            print(f"{key}: {value}")

    print(f"\n请求头验证: {'通过' if generator.validate_headers(headers) else '失败'}")

    # 统计信息
    stats = generator.get_field_statistics()
    print(f"\n统计信息:")
    print(f"固定字段数: {stats['fixed_fields_count']}")
    print(f"动态字段数: {stats['dynamic_patterns_count']}")
    print(f"基础请求头数: {stats['base_headers_count']}")
