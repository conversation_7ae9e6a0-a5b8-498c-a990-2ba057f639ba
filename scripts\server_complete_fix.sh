#!/bin/bash

# 完整的服务器修复方案
echo "🔧 完整的服务器修复..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 检查并创建虚拟环境
echo "🔍 检查虚拟环境..."
if [ ! -d "/home/<USER>/venv" ]; then
    echo "❌ 虚拟环境不存在，创建新的虚拟环境..."
    cd /home/<USER>
    python3 -m venv venv
    echo "✅ 虚拟环境已创建"
fi

# 3. 激活虚拟环境并安装依赖
echo "🔄 激活虚拟环境并安装依赖..."
source /home/<USER>/venv/bin/activate

# 安装基础依赖
pip install --upgrade pip
pip install fastapi uvicorn pydantic python-multipart aiofiles

# 安装其他必要依赖
pip install requests aiohttp asyncio-mqtt websockets
pip install numpy pandas cryptography
pip install pytest pytest-asyncio

echo "✅ 依赖安装完成"

# 4. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 5. 清理缓存
echo "🧹 清理Python缓存..."
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 6. 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p logs config

# 7. 创建配置文件
echo "📝 创建配置文件..."
cat > config/config.json << 'CONFIG'
{
    "api": {
        "host": "0.0.0.0",
        "port": 8000,
        "workers": 1
    },
    "device_pool": {
        "size": 30,
        "max_concurrent": 10
    },
    "bypass": {
        "default_strategy": "adaptive",
        "timeout": 30
    },
    "logging": {
        "level": "INFO",
        "file": "logs/app.log"
    }
}
CONFIG

# 8. 检查并修复API服务文件
echo "🔧 检查并修复API服务文件..."
python3 << 'FIX_API_SERVICE'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

# 读取API服务文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

print('🔧 检查API服务结构...')

# 检查是否是新版本的APIService类结构
if 'class APIService:' in content and 'async def initialize(self):' in content:
    print('✅ 发现新版本APIService类结构')
    
    # 检查是否有正确的startup事件
    if '@api_service.app.on_event("startup")' in content:
        print('✅ startup事件已存在')
    else:
        print('❌ 缺少startup事件，需要添加')
        
        # 在文件末尾添加startup事件
        startup_code = '''

# 创建API服务实例并启动
if __name__ == "__main__":
    import uvicorn
    
    # 创建API服务实例
    api_service = APIService()
    
    @api_service.app.on_event("startup")
    async def startup_event():
        try:
            await api_service.initialize()
            api_service.logger.info("API服务启动初始化完成")
        except Exception as e:
            api_service.logger.error(f"API服务启动初始化失败: {e}")
            raise e
    
    # 启动服务
    uvicorn.run(
        api_service.app,
        host=api_service.config.host,
        port=api_service.config.port,
        log_level=api_service.config.log_level.lower()
    )
else:
    # 用于uvicorn命令行启动
    api_service = APIService()
    
    @api_service.app.on_event("startup")
    async def startup_event():
        try:
            await api_service.initialize()
            api_service.logger.info("API服务启动初始化完成")
        except Exception as e:
            api_service.logger.error(f"API服务启动初始化失败: {e}")
            raise e
    
    # 导出app供uvicorn使用
    app = api_service.app
'''
        content += startup_code
        print('✅ startup事件已添加')

else:
    print('❌ 未发现正确的APIService类结构')
    print('需要重新上传完整的项目代码')
    exit(1)

# 写入修复后的文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ API服务文件修复完成')
FIX_API_SERVICE

if [ $? -ne 0 ]; then
    echo "❌ API服务文件修复失败"
    deactivate
    exit 1
fi

# 9. 验证语法
echo "🔍 验证语法..."
cd src
python3 -m py_compile core/api_service.py
if [ $? -eq 0 ]; then
    echo "✅ 语法验证通过"
else
    echo "❌ 语法验证失败"
    deactivate
    exit 1
fi

# 10. 测试模块导入
echo "🔍 测试模块导入..."
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    from core.api_service import app
    print('✅ API服务模块导入成功')
except Exception as e:
    print(f'❌ API服务模块导入失败: {e}')
    exit(1)

try:
    from core.bypass_engine import BypassEngine
    print('✅ 绕过引擎模块导入成功')
except Exception as e:
    print(f'❌ 绕过引擎模块导入失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 模块导入测试失败"
    deactivate
    exit 1
fi

# 11. 更新Supervisor配置
echo "🔧 更新Supervisor配置..."
sudo tee /etc/supervisor/conf.d/starbucks_bypass.conf > /dev/null << 'SUPERVISOR_CONFIG'
[program:starbucks_bypass]
command=/home/<USER>/venv/bin/python -m uvicorn core.api_service:app --host 0.0.0.0 --port 8000
directory=/home/<USER>/apps/starbucks_bypass_tester/src
user=starbucks
autostart=true
autorestart=true
stderr_logfile=/home/<USER>/apps/starbucks_bypass_tester/logs/error.log
stdout_logfile=/home/<USER>/apps/starbucks_bypass_tester/logs/output.log
environment=PYTHONPATH="/home/<USER>/apps/starbucks_bypass_tester/src"
SUPERVISOR_CONFIG

# 12. 重新加载Supervisor配置
echo "🔄 重新加载Supervisor配置..."
sudo supervisorctl reread
sudo supervisorctl update

# 13. 启动服务
echo "🚀 启动服务..."
cd /home/<USER>/apps/starbucks_bypass_tester
sudo supervisorctl start starbucks_bypass

# 14. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 15. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 16. 检查日志
echo "📋 检查启动日志..."
echo "输出日志:"
tail -15 logs/output.log 2>/dev/null || echo "无输出日志"
echo ""
echo "错误日志:"
tail -10 logs/error.log 2>/dev/null || echo "无错误日志"

# 17. 测试健康检查
echo "🧪 测试健康检查..."
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
    echo "$health_response"
else
    echo "❌ 健康检查失败"
    echo "$health_response"
fi

# 18. 测试单次绕过API
echo "🧪 测试单次绕过API..."
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
    echo ""
    echo "🎉 服务修复成功！现在运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
    echo ""
    echo "⚠️ 需要查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
