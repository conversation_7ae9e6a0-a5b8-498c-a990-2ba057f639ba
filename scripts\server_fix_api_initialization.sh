#!/bin/bash

# 修复API服务初始化问题
echo "🔧 修复API服务初始化问题..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 4. 备份当前API服务文件
echo "💾 备份当前API服务文件..."
cp src/core/api_service.py src/core/api_service.py.backup.$(date +%Y%m%d_%H%M%S)

# 5. 修复DeviceFingerprintEngine初始化问题
echo "🔧 修复DeviceFingerprintEngine初始化..."
python3 << 'FIX_FINGERPRINT_ENGINE'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

# 读取API服务文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 修复DeviceFingerprintEngine初始化
# 查找并替换错误的初始化方式
if 'self.fingerprint_engine = DeviceFingerprintEngine()' in content:
    content = content.replace(
        'self.fingerprint_engine = DeviceFingerprintEngine()',
        'self.fingerprint_engine = DeviceFingerprintEngine(self.config_manager)'
    )
    print("✅ 修复了DeviceFingerprintEngine初始化")
else:
    print("ℹ️ DeviceFingerprintEngine初始化已经正确")

# 确保config_manager在fingerprint_engine之前初始化
if 'self.config_manager = ConfigManager()' not in content:
    # 在fingerprint_engine初始化之前添加config_manager
    content = content.replace(
        '# 初始化核心组件',
        '''# 初始化核心组件
        self.config_manager = ConfigManager()'''
    )
    print("✅ 添加了config_manager初始化")

# 写回文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("✅ API服务文件修复完成")
FIX_FINGERPRINT_ENGINE

# 6. 修复supervisor配置
echo "🔧 修复supervisor配置..."
sudo tee /etc/supervisor/conf.d/starbucks_bypass.conf > /dev/null << 'EOF'
[program:starbucks_bypass]
command=/home/<USER>/venv/bin/python -m uvicorn core.api_service:app --host 0.0.0.0 --port 8000
directory=/home/<USER>/apps/starbucks_bypass_tester/src
user=starbucks
autostart=true
autorestart=true
stderr_logfile=/home/<USER>/apps/starbucks_bypass_tester/logs/error.log
stdout_logfile=/home/<USER>/apps/starbucks_bypass_tester/logs/output.log
environment=PYTHONPATH="/home/<USER>/apps/starbucks_bypass_tester/src"
startsecs=10
startretries=3
stopwaitsecs=10
EOF

echo "✅ supervisor配置已更新"

# 7. 创建日志目录
echo "📁 创建日志目录..."
mkdir -p logs
chmod 755 logs
touch logs/output.log logs/error.log
chmod 644 logs/output.log logs/error.log

# 8. 验证修复后的代码
echo "🔍 验证修复后的代码..."
cd src

echo "测试模块导入:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from config.config_manager import ConfigManager
    print('✅ ConfigManager 导入成功')
    
    from core.device_fingerprint_engine import DeviceFingerprintEngine
    print('✅ DeviceFingerprintEngine 导入成功')
    
    # 测试正确的初始化方式
    config_manager = ConfigManager()
    fingerprint_engine = DeviceFingerprintEngine(config_manager)
    print('✅ DeviceFingerprintEngine 初始化成功')
    
    from core.api_service import create_app
    print('✅ create_app 导入成功')
    
    app = create_app()
    print('✅ app 创建成功')
    print(f'app类型: {type(app)}')
    
except Exception as e:
    print(f'❌ 验证失败: {e}')
    import traceback
    traceback.print_exc()
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 代码验证失败"
    deactivate
    exit 1
fi

# 9. 重新加载supervisor配置
echo "🔄 重新加载supervisor配置..."
sudo supervisorctl reread
sudo supervisorctl update

# 10. 测试手动启动
echo "🧪 测试手动启动..."
cd /home/<USER>/apps/starbucks_bypass_tester/src

echo "设置环境变量:"
export PYTHONPATH="/home/<USER>/apps/starbucks_bypass_tester/src"
echo "PYTHONPATH=$PYTHONPATH"

echo "测试uvicorn启动:"
timeout 15s /home/<USER>/venv/bin/python -m uvicorn core.api_service:app --host 0.0.0.0 --port 8000 &
test_pid=$!

echo "测试进程ID: $test_pid"
sleep 10

# 检查进程状态
if kill -0 $test_pid 2>/dev/null; then
    echo "✅ uvicorn手动启动成功"
    
    # 测试API响应
    echo "🧪 测试API响应..."
    health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
    if echo "$health_response" | grep -q '"status"'; then
        echo "✅ API响应正常: $health_response"
    else
        echo "❌ API响应异常: $health_response"
    fi
    
    # 停止测试进程
    kill $test_pid
    wait $test_pid 2>/dev/null
    echo "✅ 测试进程已停止"
else
    echo "❌ uvicorn手动启动失败"
    echo "检查错误日志:"
    tail -10 /home/<USER>/apps/starbucks_bypass_tester/logs/error.log 2>/dev/null || echo "无错误日志"
fi

# 11. 启动supervisor服务
echo "🚀 启动supervisor服务..."
sudo supervisorctl start starbucks_bypass

# 12. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 13. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 14. 检查启动日志
echo "📋 检查启动日志..."
echo "=== 最近的输出日志 ==="
tail -10 logs/output.log 2>/dev/null || echo "无输出日志"

echo ""
echo "=== 最近的错误日志 ==="
tail -10 logs/error.log 2>/dev/null || echo "无错误日志"

# 15. 测试所有API接口
echo "🧪 测试所有API接口..."

# 等待更长时间确保服务完全启动
sleep 10

echo "1. 测试健康检查:"
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过: $health_response"
else
    echo "❌ 健康检查失败: $health_response"
fi

echo ""
echo "2. 测试信息接口:"
info_response=$(curl -s http://localhost:8000/info 2>/dev/null)
if echo "$info_response" | grep -q '"name"'; then
    echo "✅ 信息接口通过"
else
    echo "❌ 信息接口失败: $info_response"
fi

echo ""
echo "3. 测试设备接口:"
devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null)
if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ 设备接口通过"
else
    echo "❌ 设备接口失败: $devices_response"
fi

echo ""
echo "4. 测试统计接口:"
stats_response=$(curl -s http://localhost:8000/stats 2>/dev/null)
if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ 统计接口通过"
else
    echo "❌ 统计接口失败: $stats_response"
fi

echo ""
echo "5. 测试单次绕过:"
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
else
    echo "❌ 单次绕过API测试失败: $bypass_response"
fi

echo ""
echo "6. 测试批量绕过:"
batch_response=$(curl -s -X POST http://localhost:8000/bypass/batch \
  -H "Content-Type: application/json" \
  -d '{
    "requests": [
      {
        "target_url": "https://httpbin.org/get",
        "method": "GET",
        "strategy": "adaptive"
      }
    ]
  }' 2>/dev/null)

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ 批量绕过API测试成功"
else
    echo "❌ 批量绕过API测试失败: $batch_response"
fi

# 16. 统计测试结果
echo ""
echo "🎯 API测试总结:"
echo "================================"

success_count=0
total_count=6

# 检查各个接口
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ /health - 通过"
    ((success_count++))
else
    echo "❌ /health - 失败"
fi

if echo "$info_response" | grep -q '"name"'; then
    echo "✅ /info - 通过"
    ((success_count++))
else
    echo "❌ /info - 失败"
fi

if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ /devices - 通过"
    ((success_count++))
else
    echo "❌ /devices - 失败"
fi

if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ /stats - 通过"
    ((success_count++))
else
    echo "❌ /stats - 失败"
fi

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ /bypass/single - 通过"
    ((success_count++))
else
    echo "❌ /bypass/single - 失败"
fi

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ /bypass/batch - 通过"
    ((success_count++))
else
    echo "❌ /bypass/batch - 失败"
fi

echo "================================"
echo "通过率: $success_count/$total_count ($(( success_count * 100 / total_count ))%)"

if [ $success_count -eq $total_count ]; then
    echo ""
    echo "🎉 所有API测试通过！服务修复成功！"
    echo "现在可以运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo ""
    echo "⚠️ 部分API测试失败，需要进一步调试"
    echo "查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
