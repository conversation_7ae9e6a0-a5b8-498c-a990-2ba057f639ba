#!/bin/bash

# 服务器文件重建脚本 - 完全重新创建损坏的文件
echo "🚨 服务器文件重建 - 解决备份文件损坏问题..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 备份当前损坏的文件
echo "📦 备份损坏文件..."
cp src/core/api_service.py src/core/api_service.py.damaged.$(date +%Y%m%d_%H%M%S)
cp src/core/bypass_engine.py src/core/bypass_engine.py.damaged.$(date +%Y%m%d_%H%M%S)
cp src/core/device_fingerprint_engine.py src/core/device_fingerprint_engine.py.damaged.$(date +%Y%m%d_%H%M%S)

# 3. 创建文件重建脚本
echo "🔧 创建文件重建脚本..."
cat > rebuild_files.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil

def rebuild_bypass_engine():
    """重建 bypass_engine.py - 修复类型注解"""
    print("🔧 重建 bypass_engine.py...")
    
    # 读取原始文件
    with open('src/core/bypass_engine.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 清理所有可能的重复替换
    content = content.replace("fingerprint_engine: 'DeviceFingerprintEngine' = None", "fingerprint_engine: DeviceFingerprintEngine = None")
    content = content.replace("fingerprint: 'DeviceFingerprint') -> float:", "fingerprint: DeviceFingerprint) -> float:")
    
    # 应用正确的修复
    content = content.replace(
        "fingerprint_engine: DeviceFingerprintEngine = None",
        "fingerprint_engine: 'DeviceFingerprintEngine' = None"
    )
    
    content = content.replace(
        "fingerprint: DeviceFingerprint) -> float:",
        "fingerprint: 'DeviceFingerprint') -> float:"
    )
    
    with open('src/core/bypass_engine.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ bypass_engine.py 重建完成")

def rebuild_api_service():
    """重建 api_service.py - 清理重复替换并应用正确修复"""
    print("🔧 重建 api_service.py...")
    
    # 读取原始文件
    with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 清理所有重复的替换
    import re
    
    # 清理重复的 data= 前缀
    content = re.sub(r'data=data=data=data=', '', content)
    content = re.sub(r'data=data=data=', '', content)
    content = re.sub(r'data=data=', '', content)
    
    # 清理重复的 strategy= 前缀
    content = re.sub(r'strategy=strategy=strategy=strategy=', '', content)
    content = re.sub(r'strategy=strategy=strategy=', '', content)
    content = re.sub(r'strategy=strategy=', '', content)
    
    # 恢复到原始状态
    content = content.replace("await self.bypass_engine.get_bypass_statistics()", "self.bypass_engine.get_bypass_stats()")
    content = content.replace("data=request.data,", "request.data,")
    content = content.replace("data=bypass_req.data,", "bypass_req.data,")
    content = content.replace("strategy=strategy", "strategy")
    
    # 恢复返回值访问
    content = content.replace("result['success']", "result.success")
    content = content.replace("result['risk_level']", "result.risk_level.value")
    content = content.replace("result['confidence']", "result.confidence")
    content = content.replace("result['fingerprint_quality']", "result.fingerprint_quality")
    content = content.replace("result['bypass_techniques']", "result.bypass_techniques")
    content = content.replace("result['warnings']", "result.warnings")
    content = content.replace("result['execution_time']", "result.execution_time")
    content = content.replace("result['timestamp']", "result.timestamp.isoformat()")
    
    # 现在应用正确的修复
    # 修复1: 方法名
    content = content.replace(
        "self.bypass_engine.get_bypass_stats()",
        "await self.bypass_engine.get_bypass_statistics()"
    )
    
    # 修复2: 参数传递 - 逐行处理
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if "request.data," in line and "data=" not in line:
            lines[i] = line.replace("request.data,", "data=request.data,")
        
        if "bypass_req.data," in line and "data=" not in line:
            lines[i] = line.replace("bypass_req.data,", "data=bypass_req.data,")
        
        if line.strip() == "strategy":
            lines[i] = line.replace("strategy", "strategy=strategy")
    
    content = '\n'.join(lines)
    
    # 修复3: 返回值访问
    content = content.replace("result.success", "result['success']")
    content = content.replace("result.risk_level.value", "result['risk_level']")
    content = content.replace("result.confidence", "result['confidence']")
    content = content.replace("result.fingerprint_quality", "result['fingerprint_quality']")
    content = content.replace("result.bypass_techniques", "result['bypass_techniques']")
    content = content.replace("result.warnings", "result['warnings']")
    content = content.replace("result.execution_time", "result['execution_time']")
    content = content.replace("result.timestamp.isoformat()", "result['timestamp']")
    
    with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ api_service.py 重建完成")

def rebuild_device_fingerprint():
    """重建 device_fingerprint_engine.py - 添加缺失方法"""
    print("🔧 重建 device_fingerprint_engine.py...")
    
    with open('src/core/device_fingerprint_engine.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加 success_rate 字段（如果不存在）
    if 'success_rate: float = 1.0' not in content:
        target = 'additional_headers: Optional[Dict[str, str]] = None  # 添加additional_headers参数以兼容测试'
        if target in content:
            content = content.replace(
                target,
                target + '\n    success_rate: float = 1.0  # 添加成功率字段'
            )
    
    # 添加方法（如果不存在）
    if 'def is_available(self) -> bool:' not in content:
        methods_code = '''
    
    def is_available(self) -> bool:
        """检查设备指纹是否可用"""
        if not self.is_active:
            return False
        
        # 检查冷却时间
        if self.last_used:
            from datetime import timedelta
            cooldown_minutes = 5  # 默认冷却时间5分钟
            cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
            if datetime.now() < cooldown_end:
                return False
        
        return True
    
    def get_cooldown_remaining(self) -> float:
        """获取剩余冷却时间（分钟）"""
        if not self.last_used:
            return 0.0
        
        from datetime import timedelta
        cooldown_minutes = 5  # 默认冷却时间5分钟
        cooldown_end = self.last_used + timedelta(minutes=cooldown_minutes)
        now = datetime.now()
        
        if now >= cooldown_end:
            return 0.0
        
        remaining = cooldown_end - now
        return remaining.total_seconds() / 60.0  # 转换为分钟'''
        
        # 在 success_rate 字段后添加方法
        target = 'success_rate: float = 1.0  # 添加成功率字段'
        if target in content:
            content = content.replace(target, target + methods_code)
    
    with open('src/core/device_fingerprint_engine.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ device_fingerprint_engine.py 重建完成")

def verify_syntax():
    """验证语法"""
    print("🔍 验证语法...")
    files = [
        'src/core/bypass_engine.py',
        'src/core/api_service.py', 
        'src/core/device_fingerprint_engine.py'
    ]
    
    all_ok = True
    for file in files:
        result = os.system(f'python3 -m py_compile {file}')
        if result == 0:
            print(f"✅ {file} 语法正确")
        else:
            print(f"❌ {file} 语法错误")
            all_ok = False
    
    return all_ok

def main():
    """主函数"""
    print("🚀 开始文件重建...")
    
    try:
        rebuild_bypass_engine()
        rebuild_api_service()
        rebuild_device_fingerprint()
        
        if verify_syntax():
            print("🎉 所有文件重建完成且语法正确！")
            return True
        else:
            print("❌ 语法验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 重建失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
EOF

# 4. 运行文件重建
echo "🔧 运行文件重建..."
python3 rebuild_files.py

# 检查重建结果
if [ $? -eq 0 ]; then
    echo "✅ 文件重建成功"
else
    echo "❌ 文件重建失败"
    exit 1
fi

# 5. 清理临时文件
rm -f rebuild_files.py emergency_fix.py safe_repair.py

# 6. 重启服务
echo "🚀 重启服务..."
sudo supervisorctl start starbucks_bypass

# 7. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 8. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 9. 测试API
echo "🧪 测试API..."
echo "健康检查:"
curl -s http://localhost:8000/health

echo -e "\n统计信息:"
curl -s http://localhost:8000/stats | jq '.' 2>/dev/null || curl -s http://localhost:8000/stats

echo -e "\n设备列表:"
curl -s http://localhost:8000/devices | jq '.' 2>/dev/null || curl -s http://localhost:8000/devices

echo -e "\n单次绕过测试:"
curl -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET", 
    "strategy": "adaptive"
  }' | jq '.' 2>/dev/null || echo "单次绕过测试完成"

echo -e "\n🎉 文件重建和修复完成！"
