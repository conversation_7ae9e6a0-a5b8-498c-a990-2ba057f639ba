#!/bin/bash

# [符号][符号][符号][符号][符号][符号][符号][符号][符号]
# [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]

echo "=== [符号][符号][符号][符号][符号][符号][符号][符号] ==="

APP_DIR="/home/<USER>/apps/starbucks_bypass_tester"
cd "$APP_DIR"

# 1. [符号][符号][符号][符号][符号][符号]
echo "1. [符号][符号][符号][符号][符号][符号]..."
echo "[符号][符号][符号][符号]: $(whoami)"
echo "[符号][符号][符号][符号]: $(pwd)"
echo "Python[符号][符号]: $(python3 --version)"

# 2. [符号][符号][符号][符号][符号][符号][符号][符号]
echo "2. [符号][符号][符号][符号][符号][符号][符号][符号]..."
sudo supervisorctl stop starbucks_bypass 2>/dev/null || true
sudo pkill -f "python.*main.py" 2>/dev/null || true
sudo pkill -f "starbucks_bypass" 2>/dev/null || true
sudo fuser -k 8000/tcp 2>/dev/null || true
sleep 2

# 3. [符号][符号][符号][符号][符号][符号]
echo "3. [符号][符号][符号][符号][符号][符号]..."
if sudo ss -tlnp | grep -q :8000; then
    echo "   [[符号][符号]]  [符号][符号]8000[符号][符号][符号][符号]"
    sudo ss -tlnp | grep :8000
    sudo fuser -k 8000/tcp 2>/dev/null || true
    sleep 2
else
    echo "   [[符号][符号]] [符号][符号]8000[符号][符号]"
fi

# 4. [符号][符号][符号][符号][符号][符号]
echo "4. [符号][符号][符号][符号][符号][符号]..."
export PYTHONPATH="$APP_DIR/src"
echo "   PYTHONPATH=$PYTHONPATH"

# 5. [符号][符号][符号][符号][符号][符号]
echo "5. [符号][符号][符号][符号][符号][符号]..."
echo "   main.py: $([ -f main.py ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
echo "   src/core/api_service.py: $([ -f src/core/api_service.py ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"
echo "   venv/bin/python: $([ -f venv/bin/python ] && echo "[符号][符号]" || echo "[符号][符号][符号]")"

# 6. [符号][符号]Python[符号][符号]
echo "6. [符号][符号]Python[符号][符号]..."
if python3 -m py_compile main.py; then
    echo "   [[符号][符号]] main.py [符号][符号][符号][符号]"
else
    echo "   [[符号][符号]] main.py [符号][符号][符号][符号]"
    exit 1
fi

if python3 -m py_compile src/core/api_service.py; then
    echo "   [[符号][符号]] api_service.py [符号][符号][符号][符号]"
else
    echo "   [[符号][符号]] api_service.py [符号][符号][符号][符号]"
    exit 1
fi

# 7. [符号][符号][符号][符号][符号][符号]
echo "7. [符号][符号][符号][符号][符号][符号]..."
if python3 -c "from core.api_service import APIService; print('APIService[符号][符号][符号][符号]')"; then
    echo "   [[符号][符号]] APIService[符号][符号][符号][符号]"
else
    echo "   [[符号][符号]] APIService[符号][符号][符号][符号]"
    echo "   [符号][符号][符号][符号][符号][符号][符号][符号]:"
    python3 -c "from core.api_service import APIService" 2>&1 || true
    exit 1
fi

# 8. [符号][符号][符号][符号][符号][符号][符号][符号][符号]
echo "8. [符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
cat > start_simple_service.py << 'EOF'
#!/usr/bin/env python3
"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号]
[符号][符号][符号][符号][符号][符号][符号]API[符号][符号][符号][符号]
"""

import sys
import os
from pathlib import Path

# [符号][符号]Python[符号][符号]
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

try:
    from fastapi import FastAPI
    import uvicorn
    
    # [符号][符号][符号][符号][符号]FastAPI[符号][符号]
    app = FastAPI(title="[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]", version="2.0")
    
    @app.get("/")
    async def root():
        return {"message": "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]", "version": "2.0", "status": "running"}
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "message": "[符号][符号][符号][符号][符号][符号]"}
    
    @app.get("/test")
    async def test_endpoint():
        return {"test": "success", "message": "[符号][符号][符号][符号][符号][符号]"}
    
    if __name__ == "__main__":
        print("[符号][符号][符号][符号]API[符号][符号]...")
        print("[符号][符号][符号][符号]: http://0.0.0.0:8000")
        print("[符号][符号][符号][符号]: http://0.0.0.0:8000/health")
        print("[符号] Ctrl+C [符号][符号][符号][符号]")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )

except Exception as e:
    print(f"[符号][符号][符号][符号]: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF

chmod +x start_simple_service.py

# 9. [符号][符号][符号][符号][符号][符号]
echo "9. [符号][符号][符号][符号][符号][符号]..."
echo "   [符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]..."
timeout 10s python3 start_simple_service.py &
SIMPLE_PID=$!
sleep 5

if kill -0 $SIMPLE_PID 2>/dev/null; then
    echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]"
    
    # [符号][符号]API
    if curl -s http://localhost:8000/health > /dev/null; then
        echo "   [[符号][符号]] API[符号][符号][符号][符号]"
        echo "   [符号][符号][符号][符号][符号][符号]:"
        curl -s http://localhost:8000/health
    else
        echo "   [[符号][符号]] API[符号][符号][符号]"
    fi
    
    # [符号][符号][符号][符号][符号][符号]
    kill $SIMPLE_PID 2>/dev/null || true
    sleep 2
else
    echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号][符号]"
    wait $SIMPLE_PID
    echo "   [符号][符号][符号][符号]: $?"
fi

# 10. [符号][符号]main.py[符号][符号][符号][符号][符号][符号][符号]
echo "10. [符号][符号]main.py[符号][符号][符号][符号][符号][符号][符号]..."
cp main.py main.py.backup.$(date +%Y%m%d_%H%M%S)

cat > main_simple.py << 'EOF'
#!/usr/bin/env python3
"""
[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号] - [符号][符号][符号][符号][符号][符号]
"""

import sys
import os
import asyncio
import argparse
from pathlib import Path

# [符号][符号]Python[符号][符号]
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def main():
    """[符号][符号][符号][符号][符号]"""
    parser = argparse.ArgumentParser(description="[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]")
    parser.add_argument("command", nargs="?", default="server", help="[符号][符号]")
    parser.add_argument("--port", type=int, default=8000, help="[符号][符号]")
    parser.add_argument("--host", default="0.0.0.0", help="[符号][符号]")
    
    args = parser.parse_args()
    
    if args.command == "server":
        print("[符号][符号]API[符号][符号][符号]...")
        start_api_server(args.host, args.port)
    else:
        print("[符号][符号][符号][符号][符号][符号][符号]API[符号][符号][符号]...")
        start_api_server(args.host, args.port)

def start_api_server(host="0.0.0.0", port=8000):
    """[符号][符号]API[符号][符号][符号]"""
    try:
        from fastapi import FastAPI
        import uvicorn
        
        app = FastAPI(title="[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]", version="2.0")
        
        @app.get("/")
        async def root():
            return {"message": "[符号][符号][符号][符号][符号][符号][符号][符号][符号][符号][符号]", "version": "2.0"}
        
        @app.get("/health")
        async def health():
            return {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}
        
        print(f"[符号][符号][符号][符号]: {host}:{port}")
        uvicorn.run(app, host=host, port=port, log_level="info")
        
    except Exception as e:
        print(f"[符号][符号][符号][符号][符号][符号]: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
EOF

# 11. [符号][符号][符号][符号][符号][符号]
echo "11. [符号][符号][符号][符号][符号][符号]..."
mv main.py main_original.py
mv main_simple.py main.py
chmod +x main.py

# 12. [符号][符号][符号][符号]main.py
echo "12. [符号][符号][符号][符号]main.py..."
timeout 10s python3 main.py server --port 8000 --host 0.0.0.0 &
MAIN_PID=$!
sleep 5

if kill -0 $MAIN_PID 2>/dev/null; then
    echo "   [[符号][符号]] [符号]main.py[符号][符号][符号][符号]"
    
    # [符号][符号]API
    if curl -s http://localhost:8000/health > /dev/null; then
        echo "   [[符号][符号]] API[符号][符号][符号][符号]"
        curl -s http://localhost:8000/health
    else
        echo "   [[符号][符号]] API[符号][符号][符号]"
    fi
    
    kill $MAIN_PID 2>/dev/null || true
    sleep 2
else
    echo "   [[符号][符号]] [符号]main.py[符号][符号][符号][符号]"
fi

# 13. [符号][符号][符号][符号]Supervisor
echo "13. [符号][符号][符号][符号]Supervisor..."
sudo tee /etc/supervisor/conf.d/starbucks_bypass.conf > /dev/null << EOF
[program:starbucks_bypass]
command=$APP_DIR/venv/bin/python main.py server --port 8000 --host 0.0.0.0
directory=$APP_DIR
user=starbucks
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/starbucks_bypass.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile=/var/log/starbucks_bypass_error.log
environment=PYTHONPATH="$APP_DIR/src"
startsecs=10
startretries=3
EOF

# 14. [符号][符号][符号][符号]Supervisor[符号][符号]
echo "14. [符号][符号][符号][符号]Supervisor[符号][符号]..."
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start starbucks_bypass

# 15. [符号][符号][符号][符号][符号][符号]
echo "15. [符号][符号][符号][符号][符号][符号]..."
sleep 10

SERVICE_STATUS=$(sudo supervisorctl status starbucks_bypass 2>/dev/null || echo "supervisor error")
echo "[符号][符号][符号][符号]: $SERVICE_STATUS"

if echo "$SERVICE_STATUS" | grep -q "RUNNING"; then
    echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号][符号]"
    
    # [符号][符号]API
    if curl -s http://localhost:8000/health > /dev/null; then
        echo "   [[符号][符号]] API[符号][符号][符号][符号]"
        echo "   [符号][符号][符号][符号][符号][符号]:"
        curl -s http://localhost:8000/health | jq . 2>/dev/null || curl -s http://localhost:8000/health
        echo ""
        echo "   [符号][符号][符号][符号]: http://your-server-ip:8094"
        echo "   API[符号][符号]: http://your-server-ip:8094/docs"
    else
        echo "   [[符号][符号]] API[符号][符号][符号]"
    fi
else
    echo "   [[符号][符号]] [符号][符号][符号][符号][符号][符号]"
    echo "   [符号][符号][符号][符号][符号][符号]:"
    sudo supervisorctl tail starbucks_bypass 2>/dev/null || echo "[符号][符号][符号][符号][符号][符号]"
fi

echo ""
echo "=== [符号][符号][符号][符号] ==="
