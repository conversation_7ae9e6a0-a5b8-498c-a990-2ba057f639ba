#!/bin/bash

# 星巴克设备指纹绕过系统 - 日志查看脚本
# 用途: 快速查看和分析系统日志

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志文件路径
APP_LOG="logs/application.log"
SUPERVISOR_LOG="logs/supervisor.log"
ACCESS_LOG="/var/log/nginx/access.log"
ERROR_LOG="/var/log/nginx/error.log"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}星巴克设备指纹绕过系统 - 日志查看工具${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -a, --app           查看应用日志"
    echo "  -s, --supervisor    查看Supervisor日志"
    echo "  -n, --nginx         查看Nginx日志"
    echo "  -e, --errors        查看错误日志"
    echo "  -w, --warnings      查看警告日志"
    echo "  -f, --follow        实时跟踪日志"
    echo "  -t, --tail [N]      显示最后N行 (默认50)"
    echo "  -g, --grep [PATTERN] 搜索包含指定模式的日志"
    echo "  --stats             显示日志统计信息"
    echo "  --clean             清理旧日志文件"
    echo ""
    echo "示例:"
    echo "  $0 -a -t 100        显示应用日志最后100行"
    echo "  $0 -e -f            实时跟踪错误日志"
    echo "  $0 -g \"bypass\"      搜索包含'bypass'的日志"
    echo "  $0 --stats          显示日志统计信息"
}

# 检查日志文件是否存在
check_log_file() {
    local file="$1"
    local name="$2"
    
    if [ ! -f "$file" ]; then
        echo -e "${YELLOW}警告: ${name}文件不存在: $file${NC}"
        return 1
    fi
    return 0
}

# 显示日志统计信息
show_log_stats() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  日志统计信息${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    # 应用日志统计
    if check_log_file "$APP_LOG" "应用日志"; then
        echo -e "${CYAN}📋 应用日志 ($APP_LOG)${NC}"
        local file_size=$(du -h "$APP_LOG" | cut -f1)
        local total_lines=$(wc -l < "$APP_LOG")
        echo "  文件大小: $file_size"
        echo "  总行数: $total_lines"
        
        # 按级别统计
        local errors=$(grep -c "ERROR" "$APP_LOG" 2>/dev/null || echo "0")
        local warnings=$(grep -c "WARNING" "$APP_LOG" 2>/dev/null || echo "0")
        local info=$(grep -c "INFO" "$APP_LOG" 2>/dev/null || echo "0")
        
        echo "  错误数量: $errors"
        echo "  警告数量: $warnings"
        echo "  信息数量: $info"
        
        # 最近24小时的统计
        local today=$(date '+%Y-%m-%d')
        local today_errors=$(grep "$today" "$APP_LOG" | grep -c "ERROR" 2>/dev/null || echo "0")
        local today_warnings=$(grep "$today" "$APP_LOG" | grep -c "WARNING" 2>/dev/null || echo "0")
        
        echo "  今日错误: $today_errors"
        echo "  今日警告: $today_warnings"
        echo ""
    fi
    
    # Supervisor日志统计
    if check_log_file "$SUPERVISOR_LOG" "Supervisor日志"; then
        echo -e "${CYAN}📋 Supervisor日志 ($SUPERVISOR_LOG)${NC}"
        local file_size=$(du -h "$SUPERVISOR_LOG" | cut -f1)
        local total_lines=$(wc -l < "$SUPERVISOR_LOG")
        echo "  文件大小: $file_size"
        echo "  总行数: $total_lines"
        echo ""
    fi
    
    # Nginx日志统计
    if [ -f "$ACCESS_LOG" ]; then
        echo -e "${CYAN}📋 Nginx访问日志${NC}"
        local file_size=$(du -h "$ACCESS_LOG" | cut -f1)
        local total_requests=$(wc -l < "$ACCESS_LOG")
        echo "  文件大小: $file_size"
        echo "  总请求数: $total_requests"
        
        # HTTP状态码统计
        local status_200=$(grep " 200 " "$ACCESS_LOG" | wc -l 2>/dev/null || echo "0")
        local status_400=$(grep " 4[0-9][0-9] " "$ACCESS_LOG" | wc -l 2>/dev/null || echo "0")
        local status_500=$(grep " 5[0-9][0-9] " "$ACCESS_LOG" | wc -l 2>/dev/null || echo "0")
        
        echo "  成功请求(2xx): $status_200"
        echo "  客户端错误(4xx): $status_400"
        echo "  服务器错误(5xx): $status_500"
        echo ""
    fi
}

# 查看应用日志
view_app_log() {
    local lines="$1"
    local follow="$2"
    local pattern="$3"
    
    if ! check_log_file "$APP_LOG" "应用日志"; then
        return 1
    fi
    
    echo -e "${CYAN}📋 应用日志 (最后 $lines 行)${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ -n "$pattern" ]; then
        if [ "$follow" = "true" ]; then
            tail -f "$APP_LOG" | grep --color=always "$pattern"
        else
            tail -n "$lines" "$APP_LOG" | grep --color=always "$pattern"
        fi
    else
        if [ "$follow" = "true" ]; then
            tail -f "$APP_LOG"
        else
            tail -n "$lines" "$APP_LOG"
        fi
    fi
}

# 查看错误日志
view_error_log() {
    local lines="$1"
    local follow="$2"
    
    if ! check_log_file "$APP_LOG" "应用日志"; then
        return 1
    fi
    
    echo -e "${RED}[错误] 错误日志 (最后 $lines 行)${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ "$follow" = "true" ]; then
        tail -f "$APP_LOG" | grep --color=always "ERROR"
    else
        tail -n "$lines" "$APP_LOG" | grep --color=always "ERROR" || echo "没有找到错误日志"
    fi
}

# 查看警告日志
view_warning_log() {
    local lines="$1"
    local follow="$2"
    
    if ! check_log_file "$APP_LOG" "应用日志"; then
        return 1
    fi
    
    echo -e "${YELLOW}[警告]  警告日志 (最后 $lines 行)${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ "$follow" = "true" ]; then
        tail -f "$APP_LOG" | grep --color=always "WARNING"
    else
        tail -n "$lines" "$APP_LOG" | grep --color=always "WARNING" || echo "没有找到警告日志"
    fi
}

# 查看Supervisor日志
view_supervisor_log() {
    local lines="$1"
    local follow="$2"
    
    if ! check_log_file "$SUPERVISOR_LOG" "Supervisor日志"; then
        return 1
    fi
    
    echo -e "${CYAN}📋 Supervisor日志 (最后 $lines 行)${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ "$follow" = "true" ]; then
        tail -f "$SUPERVISOR_LOG"
    else
        tail -n "$lines" "$SUPERVISOR_LOG"
    fi
}

# 查看Nginx日志
view_nginx_log() {
    local lines="$1"
    local follow="$2"
    
    echo -e "${CYAN}📋 Nginx访问日志 (最后 $lines 行)${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ -f "$ACCESS_LOG" ]; then
        if [ "$follow" = "true" ]; then
            sudo tail -f "$ACCESS_LOG"
        else
            sudo tail -n "$lines" "$ACCESS_LOG"
        fi
    else
        echo "Nginx访问日志文件不存在: $ACCESS_LOG"
    fi
    
    echo ""
    echo -e "${RED}[错误] Nginx错误日志 (最后 $lines 行)${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ -f "$ERROR_LOG" ]; then
        if [ "$follow" = "true" ]; then
            sudo tail -f "$ERROR_LOG"
        else
            sudo tail -n "$lines" "$ERROR_LOG"
        fi
    else
        echo "Nginx错误日志文件不存在: $ERROR_LOG"
    fi
}

# 搜索日志
search_logs() {
    local pattern="$1"
    local lines="$2"
    
    echo -e "${CYAN}[搜索] 搜索模式: '$pattern' (最后 $lines 行)${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if check_log_file "$APP_LOG" "应用日志"; then
        echo -e "${YELLOW}应用日志中的匹配:${NC}"
        tail -n "$lines" "$APP_LOG" | grep --color=always "$pattern" || echo "没有找到匹配项"
        echo ""
    fi
    
    if check_log_file "$SUPERVISOR_LOG" "Supervisor日志"; then
        echo -e "${YELLOW}Supervisor日志中的匹配:${NC}"
        tail -n "$lines" "$SUPERVISOR_LOG" | grep --color=always "$pattern" || echo "没有找到匹配项"
        echo ""
    fi
}

# 清理日志文件
clean_logs() {
    echo -e "${YELLOW}清理日志文件...${NC}"
    
    # 备份当前日志
    if [ -f "$APP_LOG" ]; then
        local backup_name="logs/application_$(date +%Y%m%d_%H%M%S).log"
        cp "$APP_LOG" "$backup_name"
        echo "应用日志已备份到: $backup_name"
        > "$APP_LOG"
        echo "应用日志已清空"
    fi
    
    if [ -f "$SUPERVISOR_LOG" ]; then
        local backup_name="logs/supervisor_$(date +%Y%m%d_%H%M%S).log"
        cp "$SUPERVISOR_LOG" "$backup_name"
        echo "Supervisor日志已备份到: $backup_name"
        > "$SUPERVISOR_LOG"
        echo "Supervisor日志已清空"
    fi
    
    echo -e "${GREEN}日志清理完成${NC}"
}

# 默认参数
LINES=50
FOLLOW=false
PATTERN=""
ACTION="app"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -a|--app)
            ACTION="app"
            shift
            ;;
        -s|--supervisor)
            ACTION="supervisor"
            shift
            ;;
        -n|--nginx)
            ACTION="nginx"
            shift
            ;;
        -e|--errors)
            ACTION="errors"
            shift
            ;;
        -w|--warnings)
            ACTION="warnings"
            shift
            ;;
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -t|--tail)
            LINES="$2"
            shift 2
            ;;
        -g|--grep)
            PATTERN="$2"
            ACTION="search"
            shift 2
            ;;
        --stats)
            ACTION="stats"
            shift
            ;;
        --clean)
            ACTION="clean"
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行相应的操作
case $ACTION in
    app)
        view_app_log "$LINES" "$FOLLOW" "$PATTERN"
        ;;
    supervisor)
        view_supervisor_log "$LINES" "$FOLLOW"
        ;;
    nginx)
        view_nginx_log "$LINES" "$FOLLOW"
        ;;
    errors)
        view_error_log "$LINES" "$FOLLOW"
        ;;
    warnings)
        view_warning_log "$LINES" "$FOLLOW"
        ;;
    search)
        search_logs "$PATTERN" "$LINES"
        ;;
    stats)
        show_log_stats
        ;;
    clean)
        clean_logs
        ;;
    *)
        echo "无效的操作: $ACTION"
        show_help
        exit 1
        ;;
esac
