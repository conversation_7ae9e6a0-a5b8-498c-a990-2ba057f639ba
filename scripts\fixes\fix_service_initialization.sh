#!/bin/bash

# 修复服务初始化问题
echo "🔧 修复服务初始化问题..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 检查工作目录和权限
echo "🔍 检查工作目录和权限..."
cd /home/<USER>/apps/starbucks_bypass_tester

# 确保Python路径正确
export PYTHONPATH="/home/<USER>/apps/starbucks_bypass_tester/src:$PYTHONPATH"

# 3. 清理Python缓存
echo "🧹 清理Python缓存..."
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 4. 检查配置文件
echo "🔍 检查配置文件..."
if [ ! -f "config/config.json" ]; then
    echo "❌ 配置文件不存在，创建默认配置..."
    mkdir -p config
    cat > config/config.json << 'CONFIG'
{
    "api": {
        "host": "0.0.0.0",
        "port": 8000,
        "workers": 1
    },
    "device_pool": {
        "size": 30,
        "max_concurrent": 10
    },
    "bypass": {
        "default_strategy": "adaptive",
        "timeout": 30
    },
    "logging": {
        "level": "INFO",
        "file": "logs/app.log"
    }
}
CONFIG
    echo "✅ 默认配置文件已创建"
fi

# 5. 创建日志目录
echo "📁 创建日志目录..."
mkdir -p logs
chmod 755 logs

# 6. 测试模块导入
echo "🔍 测试模块导入..."
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    from core.api_service import app
    print('✅ API服务模块导入成功')
except Exception as e:
    print(f'❌ API服务模块导入失败: {e}')
    sys.exit(1)

try:
    from core.bypass_engine import BypassEngine
    print('✅ 绕过引擎模块导入成功')
except Exception as e:
    print(f'❌ 绕过引擎模块导入失败: {e}')
    sys.exit(1)

try:
    from core.device_fingerprint_engine import DeviceFingerprintEngine
    print('✅ 设备指纹引擎模块导入成功')
except Exception as e:
    print(f'❌ 设备指纹引擎模块导入失败: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 模块导入测试失败"
    exit 1
fi

# 7. 检查Supervisor配置
echo "🔍 检查Supervisor配置..."
if [ -f "/etc/supervisor/conf.d/starbucks_bypass.conf" ]; then
    echo "当前Supervisor配置:"
    cat /etc/supervisor/conf.d/starbucks_bypass.conf
    
    # 确保配置正确
    sudo tee /etc/supervisor/conf.d/starbucks_bypass.conf > /dev/null << 'SUPERVISOR_CONFIG'
[program:starbucks_bypass]
command=/home/<USER>/venv/bin/python -m uvicorn core.api_service:app --host 0.0.0.0 --port 8000
directory=/home/<USER>/apps/starbucks_bypass_tester/src
user=starbucks
autostart=true
autorestart=true
stderr_logfile=/home/<USER>/apps/starbucks_bypass_tester/logs/error.log
stdout_logfile=/home/<USER>/apps/starbucks_bypass_tester/logs/output.log
environment=PYTHONPATH="/home/<USER>/apps/starbucks_bypass_tester/src"
SUPERVISOR_CONFIG
    
    echo "✅ Supervisor配置已更新"
else
    echo "❌ Supervisor配置文件不存在"
    exit 1
fi

# 8. 重新加载Supervisor配置
echo "🔄 重新加载Supervisor配置..."
sudo supervisorctl reread
sudo supervisorctl update

# 9. 启动服务
echo "🚀 启动服务..."
sudo supervisorctl start starbucks_bypass

# 10. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 11. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 12. 检查服务日志
echo "📋 检查服务日志..."
echo "最近的错误日志:"
tail -10 logs/error.log 2>/dev/null || echo "无错误日志"

echo "最近的输出日志:"
tail -10 logs/output.log 2>/dev/null || echo "无输出日志"

# 13. 测试健康检查
echo "🧪 测试健康检查..."
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
    echo "$health_response"
else
    echo "❌ 健康检查失败"
    echo "$health_response"
fi

# 14. 测试单次绕过API
echo "🧪 测试单次绕过API..."
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
    echo ""
    echo "🎉 服务初始化修复成功！现在运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
    echo ""
    echo "⚠️ 需要查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
    echo "tail -f logs/error.log"
fi
