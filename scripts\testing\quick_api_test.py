#!/usr/bin/env python3
"""
星巴克设备指纹绕过系统 - 快速API测试
Quick API Test for Starbucks Device Fingerprint Bypass System
"""

import requests
import json
import time
import sys
from typing import Dict, Any

class QuickAPITester:
    """快速API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Quick-API-Test/1.0'})
        self.test_count = 0
        self.success_count = 0
    
    def test_request(self, method: str, endpoint: str, **kwargs) -> tuple:
        """发送测试请求"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            response = self.session.request(method, url, timeout=10, **kwargs)
            response_time = time.time() - start_time
            
            try:
                data = response.json()
            except:
                data = response.text
            
            return response.status_code, data, response_time
        except Exception as e:
            response_time = time.time() - start_time
            return 0, str(e), response_time
    
    def log_test(self, test_name: str, success: bool, details: str = "", response_time: float = 0):
        """记录测试结果"""
        self.test_count += 1
        if success:
            self.success_count += 1
            status = "[成功]"
        else:
            status = "[失败]"
        
        print(f"{status} {test_name} - {response_time:.3f}s - {details}")
    
    def test_health(self):
        """健康检查测试"""
        print("\n1. 健康检查测试")
        print("-" * 40)
        
        status, data, response_time = self.test_request('GET', '/health')
        
        if status == 200:
            self.log_test("健康检查", True, "服务正常运行", response_time)
        else:
            self.log_test("健康检查", False, f"状态码: {status}", response_time)
    
    def test_api_status(self):
        """API状态测试"""
        print("\n2. API状态测试")
        print("-" * 40)
        
        status, data, response_time = self.test_request('GET', '/api/status')
        
        if status == 200 and isinstance(data, dict):
            self.log_test("API状态", True, f"API版本: {data.get('version', 'unknown')}", response_time)
        else:
            self.log_test("API状态", False, f"状态码: {status}", response_time)
    
    def test_device_list(self):
        """设备列表测试"""
        print("\n3. 设备列表测试")
        print("-" * 40)
        
        status, data, response_time = self.test_request('GET', '/api/devices')
        
        if status == 200 and isinstance(data, dict):
            device_count = len(data.get('devices', []))
            self.log_test("设备列表", True, f"设备数量: {device_count}", response_time)
        else:
            self.log_test("设备列表", False, f"状态码: {status}", response_time)
    
    def test_fingerprint_generation(self):
        """指纹生成测试"""
        print("\n4. 指纹生成测试")
        print("-" * 40)
        
        test_data = {
            "device_id": "quick_test_device",
            "strategy": "adaptive"
        }
        
        status, data, response_time = self.test_request(
            'POST', '/api/fingerprint/generate',
            json=test_data
        )
        
        if status == 200 and isinstance(data, dict) and 'fingerprint' in data:
            self.log_test("指纹生成", True, "成功生成设备指纹", response_time)
        else:
            self.log_test("指纹生成", False, f"状态码: {status}", response_time)
    
    def test_bypass_execution(self):
        """绕过执行测试"""
        print("\n5. 绕过执行测试")
        print("-" * 40)
        
        test_data = {
            "device_id": "quick_test_device",
            "strategy": "conservative",
            "target_url": "https://app.starbucks.com/api/test"
        }
        
        status, data, response_time = self.test_request(
            'POST', '/api/bypass/execute',
            json=test_data
        )
        
        if status == 200:
            self.log_test("绕过执行", True, "绕过策略执行成功", response_time)
        else:
            self.log_test("绕过执行", False, f"状态码: {status}", response_time)
    
    def test_statistics(self):
        """统计信息测试"""
        print("\n6. 统计信息测试")
        print("-" * 40)
        
        status, data, response_time = self.test_request('GET', '/api/stats')
        
        if status == 200 and isinstance(data, dict):
            self.log_test("统计信息", True, "成功获取统计数据", response_time)
        else:
            self.log_test("统计信息", False, f"状态码: {status}", response_time)
    
    def test_concurrent_requests(self):
        """并发请求测试"""
        print("\n7. 并发请求测试")
        print("-" * 40)
        
        import concurrent.futures
        import threading
        
        def single_request():
            return self.test_request('GET', '/api/status')
        
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(single_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        successful = sum(1 for status, _, _ in results if status == 200)
        
        if successful >= 8:  # 80%成功率
            self.log_test("并发测试", True, f"{successful}/10成功", total_time)
        else:
            self.log_test("并发测试", False, f"成功率过低: {successful}/10", total_time)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("星巴克设备指纹绕过系统 - 快速API测试")
        print("=" * 60)
        print(f"测试目标: {self.base_url}")
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行所有测试
        self.test_health()
        self.test_api_status()
        self.test_device_list()
        self.test_fingerprint_generation()
        self.test_bypass_execution()
        self.test_statistics()
        self.test_concurrent_requests()
        
        # 打印总结
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        success_rate = self.success_count / self.test_count if self.test_count > 0 else 0
        
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        print(f"总测试数: {self.test_count}")
        print(f"成功测试: {self.success_count}")
        print(f"失败测试: {self.test_count - self.success_count}")
        print(f"成功率: {success_rate:.1%}")
        
        if success_rate >= 0.8:
            print("\n[结论] API服务运行正常")
        else:
            print("\n[结论] API服务存在问题，请检查服务状态")
        
        print("=" * 60)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='快速API测试工具')
    parser.add_argument('--url', default='http://localhost:8000', help='API服务地址')
    
    args = parser.parse_args()
    
    tester = QuickAPITester(args.url)
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n\n[中断] 测试被用户中断")
    except Exception as e:
        print(f"\n\n[错误] 测试执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
