#!/bin/bash

# 简单修复服务器API服务文件的导入问题
echo "🔧 修复服务器API服务文件导入问题..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 4. 备份当前文件
echo "📦 备份当前文件..."
cp src/core/api_service.py src/core/api_service.py.backup.$(date +%Y%m%d_%H%M%S)

# 5. 修复导入路径（将相对导入改为绝对导入）
echo "🔧 修复导入路径..."
sed -i 's/from \.\.utils\./from utils\./g' src/core/api_service.py
sed -i 's/from \.device_fingerprint_engine/from core.device_fingerprint_engine/g' src/core/api_service.py
sed -i 's/from \.bypass_engine/from core.bypass_engine/g' src/core/api_service.py
sed -i 's/from \.concurrency_controller/from core.concurrency_controller/g' src/core/api_service.py
sed -i 's/from \.monitor/from core.monitor/g' src/core/api_service.py

# 6. 验证语法
echo "🔍 验证语法..."
cd src
python3 -c "
import ast
with open('core/api_service.py', 'r') as f:
    content = f.read()
try:
    ast.parse(content)
    print('✅ 语法验证通过')
except SyntaxError as e:
    print(f'❌ 语法错误: 第{e.lineno}行: {e.text.strip() if e.text else \"未知\"}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 语法验证失败"
    deactivate
    exit 1
fi

# 7. 测试模块导入
echo "🔍 测试模块导入..."
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    from core.api_service import app, create_app
    print('✅ API服务模块导入成功')
except Exception as e:
    print(f'❌ API服务模块导入失败: {e}')
    exit(1)

try:
    from core.bypass_engine import BypassEngine
    print('✅ 绕过引擎模块导入成功')
except Exception as e:
    print(f'❌ 绕过引擎模块导入失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 模块导入测试失败"
    deactivate
    exit 1
fi

# 8. 启动服务
echo "🚀 启动服务..."
cd /home/<USER>/apps/starbucks_bypass_tester
sudo supervisorctl start starbucks_bypass

# 9. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 10. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 11. 检查启动日志
echo "📋 检查启动日志..."
echo "=== 最近的输出日志 ==="
tail -15 logs/output.log 2>/dev/null || echo "无输出日志"
echo ""
echo "=== 最近的错误日志 ==="
tail -10 logs/error.log 2>/dev/null || echo "无错误日志"

# 12. 测试所有API接口
echo "🧪 测试所有API接口..."

# 健康检查
echo "1. 测试健康检查:"
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
    echo "$health_response"
else
    echo "❌ 健康检查失败: $health_response"
fi

# 信息接口
echo ""
echo "2. 测试信息接口:"
info_response=$(curl -s http://localhost:8000/info 2>/dev/null)
if echo "$info_response" | grep -q '"name"'; then
    echo "✅ 信息接口通过"
    echo "$info_response"
else
    echo "❌ 信息接口失败: $info_response"
fi

# 设备接口
echo ""
echo "3. 测试设备接口:"
devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null)
if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ 设备接口通过"
    echo "设备数量: $(echo "$devices_response" | grep -o '"total":[0-9]*' | cut -d: -f2)"
else
    echo "❌ 设备接口失败: $devices_response"
fi

# 统计接口
echo ""
echo "4. 测试统计接口:"
stats_response=$(curl -s http://localhost:8000/stats 2>/dev/null)
if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ 统计接口通过"
else
    echo "❌ 统计接口失败: $stats_response"
fi

# 单次绕过
echo ""
echo "5. 测试单次绕过:"
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
fi

# 批量绕过
echo ""
echo "6. 测试批量绕过:"
batch_response=$(curl -s -X POST http://localhost:8000/bypass/batch \
  -H "Content-Type: application/json" \
  -d '{
    "requests": [
      {
        "target_url": "https://httpbin.org/get",
        "method": "GET",
        "strategy": "adaptive"
      }
    ]
  }' 2>/dev/null)

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ 批量绕过API测试成功"
    echo "响应预览:"
    echo "$batch_response" | head -3
else
    echo "❌ 批量绕过API测试失败"
    echo "错误响应:"
    echo "$batch_response"
fi

# 13. 统计测试结果
echo ""
echo "🎯 API测试总结:"
echo "================================"

success_count=0
total_count=6

# 检查各个接口
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ /health - 通过"
    ((success_count++))
else
    echo "❌ /health - 失败"
fi

if echo "$info_response" | grep -q '"name"'; then
    echo "✅ /info - 通过"
    ((success_count++))
else
    echo "❌ /info - 失败"
fi

if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ /devices - 通过"
    ((success_count++))
else
    echo "❌ /devices - 失败"
fi

if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ /stats - 通过"
    ((success_count++))
else
    echo "❌ /stats - 失败"
fi

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ /bypass/single - 通过"
    ((success_count++))
else
    echo "❌ /bypass/single - 失败"
fi

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ /bypass/batch - 通过"
    ((success_count++))
else
    echo "❌ /bypass/batch - 失败"
fi

echo "================================"
echo "通过率: $success_count/$total_count ($(( success_count * 100 / total_count ))%)"

if [ $success_count -eq $total_count ]; then
    echo ""
    echo "🎉 所有API测试通过！服务修复成功！"
    echo "现在可以运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo ""
    echo "⚠️ 部分API测试失败，需要进一步调试"
    echo "查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
