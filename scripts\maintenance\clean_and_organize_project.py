#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目代码清理和整理脚本
按照代码开发规范进行项目整理
"""

import os
import shutil
import subprocess
import json
from pathlib import Path
from typing import List, Dict, Set

class ProjectOrganizer:
    """项目整理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.excluded_dirs = {"backup_starbucks_bypass_tester", "xbk", "docs"}
        self.cache_patterns = {"__pycache__", "*.pyc", "*.pyo", ".pytest_cache"}
        self.temp_patterns = {"*.tmp", "*.temp", "*.log~", "*.bak"}
        
    def clean_cache_files(self) -> int:
        """清理缓存文件"""
        print("[信息] 开始清理缓存文件...")
        cleaned_count = 0
        
        for root, dirs, files in os.walk(self.project_root):
            # 跳过排除的目录
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            # 清理__pycache__目录
            if "__pycache__" in dirs:
                cache_dir = Path(root) / "__pycache__"
                try:
                    shutil.rmtree(cache_dir)
                    print(f"  [删除] {cache_dir}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"  [错误] 删除缓存目录失败: {cache_dir} - {e}")
            
            # 清理.pyc文件
            for file in files:
                if file.endswith(('.pyc', '.pyo')):
                    file_path = Path(root) / file
                    try:
                        file_path.unlink()
                        print(f"  [删除] {file_path}")
                        cleaned_count += 1
                    except Exception as e:
                        print(f"  [错误] 删除文件失败: {file_path} - {e}")
        
        print(f"[完成] 清理了 {cleaned_count} 个缓存文件/目录")
        return cleaned_count
    
    def verify_code_syntax(self) -> Dict[str, bool]:
        """验证代码语法"""
        print("[信息] 开始验证Python代码语法...")
        results = {}
        
        # 查找所有Python文件
        python_files = []
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        
        for py_file in python_files:
            try:
                result = subprocess.run(
                    ['python3', '-m', 'py_compile', str(py_file)],
                    capture_output=True,
                    text=True,
                    cwd=self.project_root
                )
                if result.returncode == 0:
                    print(f"  [通过] {py_file.relative_to(self.project_root)}")
                    results[str(py_file)] = True
                else:
                    print(f"  [失败] {py_file.relative_to(self.project_root)}")
                    print(f"    错误: {result.stderr}")
                    results[str(py_file)] = False
            except Exception as e:
                print(f"  [错误] 验证失败: {py_file} - {e}")
                results[str(py_file)] = False
        
        passed = sum(1 for v in results.values() if v)
        total = len(results)
        print(f"[完成] 语法验证: {passed}/{total} 文件通过")
        return results
    
    def check_import_structure(self) -> Dict[str, List[str]]:
        """检查导入结构"""
        print("[信息] 检查模块导入结构...")
        import_issues = {}
        
        # 核心模块导入测试
        test_imports = [
            ("config.config_manager", "ConfigManager"),
            ("core.api_service", "APIService"),
            ("core.bypass_engine", "BypassEngine"),
            ("core.device_fingerprint_engine", "DeviceFingerprintEngine"),
            ("core.concurrency_controller", "ConcurrencyController"),
            ("utils.logger", "get_logger"),
            ("cli.main", "StarBucksCLI")
        ]
        
        for module_path, class_name in test_imports:
            try:
                # 设置Python路径
                src_path = self.project_root / "starbucks_bypass_tester" / "src"
                import sys
                if str(src_path) not in sys.path:
                    sys.path.insert(0, str(src_path))
                
                # 尝试导入
                module = __import__(module_path, fromlist=[class_name])
                getattr(module, class_name)
                print(f"  [通过] {module_path}.{class_name}")
            except Exception as e:
                print(f"  [失败] {module_path}.{class_name} - {e}")
                if module_path not in import_issues:
                    import_issues[module_path] = []
                import_issues[module_path].append(str(e))
        
        if not import_issues:
            print("[完成] 所有核心模块导入正常")
        else:
            print(f"[警告] 发现 {len(import_issues)} 个导入问题")
        
        return import_issues
    
    def organize_scripts(self) -> int:
        """整理脚本文件"""
        print("[信息] 整理脚本文件...")
        organized_count = 0
        
        scripts_dir = self.project_root / "scripts"
        if not scripts_dir.exists():
            print("[跳过] scripts目录不存在")
            return 0
        
        # 确保所有.sh文件有执行权限
        for script_file in scripts_dir.rglob("*.sh"):
            try:
                # 添加执行权限
                script_file.chmod(0o755)
                print(f"  [权限] {script_file.name}")
                organized_count += 1
            except Exception as e:
                print(f"  [错误] 设置权限失败: {script_file} - {e}")
        
        print(f"[完成] 整理了 {organized_count} 个脚本文件")
        return organized_count
    
    def validate_config_files(self) -> Dict[str, bool]:
        """验证配置文件"""
        print("[信息] 验证配置文件...")
        results = {}
        
        config_dir = self.project_root / "starbucks_bypass_tester" / "src" / "config"
        if not config_dir.exists():
            print("[跳过] config目录不存在")
            return results
        
        # 验证JSON配置文件
        json_files = list(config_dir.glob("*.json"))
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    json.load(f)
                print(f"  [通过] {json_file.name}")
                results[str(json_file)] = True
            except Exception as e:
                print(f"  [失败] {json_file.name} - {e}")
                results[str(json_file)] = False
        
        print(f"[完成] 验证了 {len(results)} 个配置文件")
        return results
    
    def generate_structure_report(self) -> Dict:
        """生成项目结构报告"""
        print("[信息] 生成项目结构报告...")
        
        report = {
            "project_root": str(self.project_root),
            "directories": {},
            "file_counts": {},
            "total_lines": 0
        }
        
        for root, dirs, files in os.walk(self.project_root):
            # 跳过排除的目录
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            rel_path = Path(root).relative_to(self.project_root)
            if str(rel_path) == ".":
                rel_path = "root"
            else:
                rel_path = str(rel_path)
            
            # 统计文件类型
            file_types = {}
            for file in files:
                ext = Path(file).suffix.lower()
                if not ext:
                    ext = "no_extension"
                file_types[ext] = file_types.get(ext, 0) + 1
                
                # 统计Python文件行数
                if ext == ".py":
                    try:
                        file_path = Path(root) / file
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = len(f.readlines())
                            report["total_lines"] += lines
                    except:
                        pass
            
            if file_types:
                report["directories"][rel_path] = file_types
        
        # 统计总文件数
        for dir_files in report["directories"].values():
            for ext, count in dir_files.items():
                report["file_counts"][ext] = report["file_counts"].get(ext, 0) + count
        
        print(f"[完成] 项目包含 {report['total_lines']} 行Python代码")
        return report
    
    def run_full_organization(self) -> Dict:
        """执行完整的项目整理"""
        print("=" * 60)
        print("星巴克设备指纹绕过系统 - 项目代码整理")
        print("=" * 60)
        
        results = {
            "cache_cleaned": 0,
            "syntax_check": {},
            "import_check": {},
            "scripts_organized": 0,
            "config_validation": {},
            "structure_report": {}
        }
        
        try:
            # 1. 清理缓存文件
            results["cache_cleaned"] = self.clean_cache_files()
            print()
            
            # 2. 验证代码语法
            results["syntax_check"] = self.verify_code_syntax()
            print()
            
            # 3. 检查导入结构
            results["import_check"] = self.check_import_structure()
            print()
            
            # 4. 整理脚本文件
            results["scripts_organized"] = self.organize_scripts()
            print()
            
            # 5. 验证配置文件
            results["config_validation"] = self.validate_config_files()
            print()
            
            # 6. 生成结构报告
            results["structure_report"] = self.generate_structure_report()
            print()
            
            # 输出总结
            print("=" * 60)
            print("整理完成总结:")
            print(f"- 清理缓存文件: {results['cache_cleaned']} 个")
            print(f"- 语法检查通过: {sum(1 for v in results['syntax_check'].values() if v)}/{len(results['syntax_check'])} 个")
            print(f"- 导入问题: {len(results['import_check'])} 个")
            print(f"- 脚本整理: {results['scripts_organized']} 个")
            print(f"- 配置文件验证: {sum(1 for v in results['config_validation'].values() if v)}/{len(results['config_validation'])} 个")
            print(f"- 总代码行数: {results['structure_report'].get('total_lines', 0)} 行")
            print("=" * 60)
            
        except Exception as e:
            print(f"[错误] 整理过程中出现错误: {e}")
            results["error"] = str(e)
        
        return results

def main():
    """主函数"""
    organizer = ProjectOrganizer()
    results = organizer.run_full_organization()
    
    # 保存结果到文件
    results_file = Path("project_organization_results.json")
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"[信息] 整理结果已保存到: {results_file}")
    except Exception as e:
        print(f"[错误] 保存结果失败: {e}")

if __name__ == "__main__":
    main()
