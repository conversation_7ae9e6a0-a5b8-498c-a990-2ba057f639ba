#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API模型验证脚本
验证BypassResponse模型是否包含所有必需字段
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent / "starbucks_bypass_tester"
sys.path.insert(0, str(project_root / "src"))

def verify_bypass_response_model():
    """验证BypassResponse模型"""
    print("🔍 验证BypassResponse模型...")
    
    try:
        from core.api_service import BypassResponse
        from pydantic import ValidationError
        
        # 测试数据
        test_data = {
            "success": True,
            "request_id": "test_123",
            "risk_level": "low",
            "confidence": 0.8,
            "fingerprint_quality": 0.9,
            "bypass_techniques": ["header_randomization"],
            "warnings": [],
            "execution_time": 0.5,
            "timestamp": "2025-07-31T15:30:00",
            "strategy_used": "adaptive",
            "modified_fingerprint": None,
            "response_data": None
        }
        
        # 创建响应对象
        response = BypassResponse(**test_data)
        print("✅ BypassResponse模型验证成功")
        
        # 检查字段
        required_fields = [
            'success', 'request_id', 'risk_level', 'confidence',
            'fingerprint_quality', 'bypass_techniques', 'warnings',
            'execution_time', 'timestamp', 'strategy_used', 
            'modified_fingerprint', 'response_data'
        ]
        
        missing_fields = []
        for field in required_fields:
            if not hasattr(response, field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺失字段: {missing_fields}")
            return False
        else:
            print("✅ 所有必需字段都存在")
            return True
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except ValidationError as e:
        print(f"❌ 模型验证失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_api_service_file():
    """验证API服务文件内容"""
    print("🔍 验证API服务文件内容...")
    
    api_file = project_root / "src" / "core" / "api_service.py"
    
    if not api_file.exists():
        print(f"❌ API服务文件不存在: {api_file}")
        return False
    
    with open(api_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查必需的字段定义
    required_patterns = [
        'strategy_used: Optional[str] = None',
        'modified_fingerprint: Optional[Dict[str, Any]] = None',
        "strategy_used=result.get('strategy_used')",
        'modified_fingerprint=None'
    ]
    
    missing_patterns = []
    for pattern in required_patterns:
        if pattern not in content:
            missing_patterns.append(pattern)
    
    if missing_patterns:
        print(f"❌ 缺失模式: {missing_patterns}")
        return False
    else:
        print("✅ 所有必需模式都存在")
        return True

def verify_syntax():
    """验证语法"""
    print("🔍 验证语法...")
    
    api_file = project_root / "src" / "core" / "api_service.py"
    
    try:
        with open(api_file, 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, str(api_file), 'exec')
        print("✅ 语法验证通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("API模型验证工具")
    print("=" * 60)
    
    # 切换到项目目录
    os.chdir(project_root)
    
    success = True
    
    # 1. 验证语法
    if not verify_syntax():
        success = False
    
    # 2. 验证文件内容
    if not verify_api_service_file():
        success = False
    
    # 3. 验证模型
    if not verify_bypass_response_model():
        success = False
    
    if success:
        print("\n🎉 所有验证通过 - API模型修复正确!")
        return True
    else:
        print("\n❌ 验证失败 - 需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
