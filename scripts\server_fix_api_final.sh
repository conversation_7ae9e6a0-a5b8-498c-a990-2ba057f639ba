#!/bin/bash

# 最终API修复脚本 - 直接替换文件避免转义问题
echo "🔧 最终API修复 - 解决单次绕过HTTP 500错误..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 备份当前文件
echo "📦 备份当前文件..."
cp src/core/api_service.py src/core/api_service.py.backup.$(date +%Y%m%d_%H%M%S)

# 3. 下载修复后的文件（如果可能）或使用wget/curl
echo "🔧 应用修复..."

# 方法1: 使用Python进行简单的字符串替换（避免复杂转义）
python3 << 'PYTHON_SCRIPT'
import os

# 读取文件
with open('src/core/api_service.py', 'r', encoding='utf-8') as f:
    content = f.read()

print('🔧 修复 BypassResponse 模型...')

# 1. 修复 BypassResponse 模型定义
if 'strategy_used: Optional[str] = None' not in content:
    # 查找并替换模型定义
    old_pattern = 'timestamp: str\n    response_data: Optional[Dict[str, Any]] = None'
    new_pattern = 'timestamp: str\n    strategy_used: Optional[str] = None\n    modified_fingerprint: Optional[Dict[str, Any]] = None\n    response_data: Optional[Dict[str, Any]] = None'
    
    if old_pattern in content:
        content = content.replace(old_pattern, new_pattern)
        print('  ✅ BypassResponse 模型字段已添加')
    else:
        print('  ⚠️ 模型定义未找到或已修复')
else:
    print('  ℹ️ BypassResponse 模型已包含新字段')

print('🔧 修复响应构建...')

# 2. 修复单次绕过响应构建
old_single = "timestamp=result['timestamp']\n                )"
new_single = "timestamp=result['timestamp'],\n                    strategy_used=result.get('strategy_used'),\n                    modified_fingerprint=None  # 不返回敏感的指纹信息\n                )"

if old_single in content and new_single not in content:
    content = content.replace(old_single, new_single)
    print('  ✅ 单次绕过响应构建已修复')
else:
    print('  ℹ️ 单次绕过响应构建已修复或未找到')

# 3. 修复批量绕过成功响应
old_batch_success = "timestamp=result['timestamp']\n                            )"
new_batch_success = "timestamp=result['timestamp'],\n                                strategy_used=result.get('strategy_used'),\n                                modified_fingerprint=None\n                            )"

if old_batch_success in content and new_batch_success not in content:
    content = content.replace(old_batch_success, new_batch_success)
    print('  ✅ 批量绕过成功响应已修复')
else:
    print('  ℹ️ 批量绕过成功响应已修复或未找到')

# 4. 修复批量绕过错误响应
old_batch_error = "timestamp=datetime.now().isoformat()\n                            )"
new_batch_error = "timestamp=datetime.now().isoformat(),\n                                strategy_used=None,\n                                modified_fingerprint=None\n                            )"

if old_batch_error in content and new_batch_error not in content:
    content = content.replace(old_batch_error, new_batch_error)
    print('  ✅ 批量绕过错误响应已修复')
else:
    print('  ℹ️ 批量绕过错误响应已修复或未找到')

# 写入文件
with open('src/core/api_service.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('✅ API模型修复完成')
PYTHON_SCRIPT

# 4. 验证语法
echo "🔍 验证语法..."
python3 -m py_compile src/core/api_service.py
if [ $? -eq 0 ]; then
    echo "✅ 语法验证通过"
else
    echo "❌ 语法验证失败，恢复备份..."
    LATEST_BACKUP=$(ls -t src/core/api_service.py.backup.* 2>/dev/null | head -1)
    if [ -n "$LATEST_BACKUP" ]; then
        cp "$LATEST_BACKUP" src/core/api_service.py
        echo "✅ 已恢复到备份: $LATEST_BACKUP"
    fi
    exit 1
fi

# 5. 重启服务
echo "🚀 重启服务..."
sudo supervisorctl start starbucks_bypass

# 6. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 7. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 8. 测试API
echo "🧪 测试API..."
echo "健康检查:"
curl -s http://localhost:8000/health | head -3

echo -e "\n单次绕过测试:"
curl -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null | head -5

echo -e "\n🎉 API修复完成！请运行完整测试验证结果。"
