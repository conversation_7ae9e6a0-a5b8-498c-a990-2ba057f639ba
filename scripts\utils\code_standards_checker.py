#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码开发规范检查脚本
严格按照docs/01_核心文档/代码开发规范.md进行检查
"""

import os
import re
import ast
from pathlib import Path
from typing import List, Dict, Tuple, Set
import json

class CodeStandardsChecker:
    """代码规范检查器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.excluded_dirs = {"backup_starbucks_bypass_tester", "xbk", "docs", "__pycache__"}
        self.violations = []
        
        # 表情符号检测模式
        self.emoji_pattern = re.compile(
            r'[\U0001F600-\U0001F64F]|'  # 表情符号
            r'[\U0001F300-\U0001F5FF]|'  # 符号和象形文字
            r'[\U0001F680-\U0001F6FF]|'  # 交通和地图符号
            r'[\U0001F1E0-\U0001F1FF]|'  # 国旗
            r'[\*********-\U000027B0]|'  # 杂项符号
            r'[\U000024C2-\U0001F251]'   # 其他符号
        )
    
    def check_emoji_usage(self, file_path: Path) -> List[Dict]:
        """检查表情符号使用（严格禁止）"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                if self.emoji_pattern.search(line):
                    violations.append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'line': line_num,
                        'type': 'emoji_violation',
                        'message': '严格禁止使用表情符号',
                        'content': line.strip()
                    })
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'file_error',
                'message': f'文件读取错误: {e}',
                'content': ''
            })
        
        return violations
    
    def check_chinese_comments(self, file_path: Path) -> List[Dict]:
        """检查中文注释要求"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            # 检查类和函数的文档字符串
            for node in ast.walk(tree):
                if isinstance(node, (ast.ClassDef, ast.FunctionDef, ast.AsyncFunctionDef)):
                    # 检查是否有文档字符串
                    docstring = ast.get_docstring(node)
                    if not docstring:
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'type': 'missing_docstring',
                            'message': f'{node.__class__.__name__} "{node.name}" 缺少中文文档字符串',
                            'content': f'{node.__class__.__name__} {node.name}'
                        })
                    elif not self._contains_chinese(docstring):
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'type': 'non_chinese_docstring',
                            'message': f'{node.__class__.__name__} "{node.name}" 文档字符串应使用中文',
                            'content': docstring[:50] + '...' if len(docstring) > 50 else docstring
                        })
        
        except SyntaxError as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': e.lineno or 0,
                'type': 'syntax_error',
                'message': f'语法错误: {e.msg}',
                'content': ''
            })
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'parse_error',
                'message': f'解析错误: {e}',
                'content': ''
            })
        
        return violations
    
    def check_naming_conventions(self, file_path: Path) -> List[Dict]:
        """检查命名规范"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                # 检查类名（PascalCase）
                if isinstance(node, ast.ClassDef):
                    if not self._is_pascal_case(node.name):
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'type': 'class_naming',
                            'message': f'类名 "{node.name}" 应使用PascalCase命名',
                            'content': f'class {node.name}'
                        })
                
                # 检查函数名（snake_case）
                elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    if not self._is_snake_case(node.name) and not node.name.startswith('_'):
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'type': 'function_naming',
                            'message': f'函数名 "{node.name}" 应使用snake_case命名',
                            'content': f'def {node.name}'
                        })
                
                # 检查变量名（snake_case）
                elif isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            if not self._is_snake_case(target.id) and not target.id.isupper():
                                violations.append({
                                    'file': str(file_path.relative_to(self.project_root)),
                                    'line': node.lineno,
                                    'type': 'variable_naming',
                                    'message': f'变量名 "{target.id}" 应使用snake_case命名',
                                    'content': f'{target.id} = ...'
                                })
        
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'naming_check_error',
                'message': f'命名检查错误: {e}',
                'content': ''
            })
        
        return violations
    
    def check_import_organization(self, file_path: Path) -> List[Dict]:
        """检查导入组织"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            import_lines = []
            for line_num, line in enumerate(lines, 1):
                stripped = line.strip()
                if stripped.startswith(('import ', 'from ')):
                    import_lines.append((line_num, stripped))
            
            # 检查导入顺序（标准库 -> 第三方 -> 本地）
            if len(import_lines) > 1:
                prev_type = None
                for line_num, import_line in import_lines:
                    current_type = self._get_import_type(import_line)
                    if prev_type and current_type < prev_type:
                        violations.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'type': 'import_order',
                            'message': '导入顺序不正确，应按标准库、第三方库、本地模块顺序',
                            'content': import_line
                        })
                    prev_type = current_type
        
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'import_check_error',
                'message': f'导入检查错误: {e}',
                'content': ''
            })
        
        return violations
    
    def check_line_length(self, file_path: Path, max_length: int = 120) -> List[Dict]:
        """检查行长度"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # 移除换行符计算长度
                line_content = line.rstrip('\n\r')
                if len(line_content) > max_length:
                    violations.append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'line': line_num,
                        'type': 'line_length',
                        'message': f'行长度 {len(line_content)} 超过限制 {max_length}',
                        'content': line_content[:50] + '...' if len(line_content) > 50 else line_content
                    })
        
        except Exception as e:
            violations.append({
                'file': str(file_path.relative_to(self.project_root)),
                'line': 0,
                'type': 'line_length_error',
                'message': f'行长度检查错误: {e}',
                'content': ''
            })
        
        return violations
    
    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        return bool(re.search(r'[\u4e00-\u9fff]', text))
    
    def _is_pascal_case(self, name: str) -> bool:
        """检查是否为PascalCase"""
        return bool(re.match(r'^[A-Z][a-zA-Z0-9]*$', name))
    
    def _is_snake_case(self, name: str) -> bool:
        """检查是否为snake_case"""
        return bool(re.match(r'^[a-z][a-z0-9_]*$', name))
    
    def _get_import_type(self, import_line: str) -> int:
        """获取导入类型（0=标准库，1=第三方，2=本地）"""
        # 简化的导入类型判断
        if any(lib in import_line for lib in ['os', 'sys', 'json', 'time', 'datetime', 're', 'pathlib']):
            return 0  # 标准库
        elif any(lib in import_line for lib in ['fastapi', 'uvicorn', 'pydantic', 'aiohttp', 'pytest']):
            return 1  # 第三方库
        else:
            return 2  # 本地模块
    
    def check_file(self, file_path: Path) -> List[Dict]:
        """检查单个文件"""
        all_violations = []
        
        # 只检查Python文件
        if file_path.suffix != '.py':
            return all_violations
        
        # 执行各项检查
        all_violations.extend(self.check_emoji_usage(file_path))
        all_violations.extend(self.check_chinese_comments(file_path))
        all_violations.extend(self.check_naming_conventions(file_path))
        all_violations.extend(self.check_import_organization(file_path))
        all_violations.extend(self.check_line_length(file_path))
        
        return all_violations
    
    def check_project(self) -> Dict:
        """检查整个项目"""
        print("开始代码规范检查...")
        
        all_violations = []
        checked_files = 0
        
        # 遍历项目文件
        for root, dirs, files in os.walk(self.project_root):
            # 跳过排除的目录
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    violations = self.check_file(file_path)
                    all_violations.extend(violations)
                    checked_files += 1
                    
                    if violations:
                        print(f"[警告] {file_path.relative_to(self.project_root)}: {len(violations)} 个问题")
                    else:
                        print(f"[通过] {file_path.relative_to(self.project_root)}")
        
        # 统计结果
        violation_types = {}
        for violation in all_violations:
            vtype = violation['type']
            violation_types[vtype] = violation_types.get(vtype, 0) + 1
        
        results = {
            'total_files_checked': checked_files,
            'total_violations': len(all_violations),
            'violation_types': violation_types,
            'violations': all_violations
        }
        
        # 输出总结
        print("\n" + "=" * 60)
        print("代码规范检查完成")
        print("=" * 60)
        print(f"检查文件数: {checked_files}")
        print(f"发现问题数: {len(all_violations)}")
        
        if violation_types:
            print("\n问题类型统计:")
            for vtype, count in sorted(violation_types.items()):
                print(f"  {vtype}: {count} 个")
        else:
            print("\n[优秀] 所有文件都符合代码规范！")
        
        return results

def main():
    """主函数"""
    checker = CodeStandardsChecker()
    results = checker.check_project()
    
    # 保存检查结果
    results_file = Path("code_standards_check_results.json")
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n[信息] 检查结果已保存到: {results_file}")
    except Exception as e:
        print(f"\n[错误] 保存结果失败: {e}")
    
    # 如果有违规，返回非零退出码
    return 1 if results['total_violations'] > 0 else 0

if __name__ == "__main__":
    exit(main())
