#!/bin/bash

# 修复服务器PyYAML依赖问题
echo "🔧 修复服务器PyYAML依赖问题..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 4. 检查PyYAML的实际导入名称
echo "🔍 检查PyYAML的实际导入名称..."
python3 -c "
import sys
print('Python版本:', sys.version)
print('已安装的包:')
import pkg_resources
for pkg in pkg_resources.working_set:
    if 'yaml' in pkg.project_name.lower():
        print(f'  {pkg.project_name}: {pkg.version}')

print('\\n测试不同的YAML导入方式:')
try:
    import yaml
    print('✅ import yaml - 成功')
except ImportError as e:
    print(f'❌ import yaml - 失败: {e}')

try:
    import PyYAML
    print('✅ import PyYAML - 成功')
except ImportError as e:
    print(f'❌ import PyYAML - 失败: {e}')
"

# 5. 重新安装PyYAML
echo "📦 重新安装PyYAML..."
pip uninstall -y PyYAML
pip install PyYAML

# 6. 验证YAML安装
echo "🔍 验证YAML安装..."
python3 -c "
try:
    import yaml
    print('✅ yaml 导入成功')
    print(f'yaml版本: {yaml.__version__}')
    
    # 测试基本功能
    test_data = {'test': 'value'}
    yaml_str = yaml.dump(test_data)
    parsed_data = yaml.safe_load(yaml_str)
    print('✅ yaml 基本功能测试通过')
except ImportError as e:
    print(f'❌ yaml 导入失败: {e}')
    exit(1)
except Exception as e:
    print(f'❌ yaml 功能测试失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ YAML安装验证失败"
    deactivate
    exit 1
fi

# 7. 修复依赖检查脚本中的包名问题
echo "🔧 修复依赖检查脚本..."
python3 -c "
# 正确的依赖检查方式
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

# 使用正确的导入名称
required_imports = [
    ('fastapi', 'fastapi'),
    ('uvicorn', 'uvicorn'), 
    ('pydantic', 'pydantic'),
    ('requests', 'requests'),
    ('aiohttp', 'aiohttp'),
    ('yaml', 'PyYAML'),  # 包名是PyYAML，但导入名是yaml
    ('psutil', 'psutil'),
    ('asyncio', 'asyncio'),
    ('json', 'json'),
    ('typing', 'typing')
]

missing_packages = []
for import_name, package_name in required_imports:
    try:
        __import__(import_name)
        print(f'✅ {package_name} ({import_name}) - 可用')
    except ImportError:
        print(f'❌ {package_name} ({import_name}) - 缺失')
        missing_packages.append(package_name)

if missing_packages:
    print(f'\\n⚠️ 缺失的包: {missing_packages}')
    exit(1)
else:
    print('\\n✅ 所有关键依赖包都已安装')
"

if [ $? -ne 0 ]; then
    echo "❌ 依赖检查失败"
    deactivate
    exit 1
fi

# 8. 验证所有关键模块导入
echo "🔍 验证所有关键模块导入..."

echo "测试 utils.logger:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from utils.logger import get_logger
    print('✅ utils.logger 导入成功')
except Exception as e:
    print(f'❌ utils.logger 导入失败: {e}')
    exit(1)
"

echo "测试 config.config_manager:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from config.config_manager import ConfigManager
    print('✅ config.config_manager 导入成功')
except Exception as e:
    print(f'❌ config.config_manager 导入失败: {e}')
    exit(1)
"

echo "测试 utils.monitor:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from utils.monitor import Monitor
    print('✅ utils.monitor 导入成功')
except Exception as e:
    print(f'❌ utils.monitor 导入失败: {e}')
    exit(1)
"

echo "测试 core.device_fingerprint_engine:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.device_fingerprint_engine import DeviceFingerprintEngine
    print('✅ core.device_fingerprint_engine 导入成功')
except Exception as e:
    print(f'❌ core.device_fingerprint_engine 导入失败: {e}')
    exit(1)
"

echo "测试 core.bypass_engine:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.bypass_engine import BypassEngine
    print('✅ core.bypass_engine 导入成功')
except Exception as e:
    print(f'❌ core.bypass_engine 导入失败: {e}')
    exit(1)
"

echo "测试 core.api_service:"
python3 -c "
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')
try:
    from core.api_service import app, create_app
    print('✅ core.api_service 导入成功')
except Exception as e:
    print(f'❌ core.api_service 导入失败: {e}')
    exit(1)
"

# 9. 启动服务
echo "🚀 启动服务..."
sudo supervisorctl start starbucks_bypass

# 10. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 11. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 12. 检查启动日志
echo "📋 检查启动日志..."
echo "=== 最近的输出日志 ==="
tail -15 logs/output.log 2>/dev/null || echo "无输出日志"
echo ""
echo "=== 最近的错误日志 ==="
tail -10 logs/error.log 2>/dev/null || echo "无错误日志"

# 13. 测试所有API接口
echo "🧪 测试所有API接口..."

# 健康检查
echo "1. 测试健康检查:"
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
    echo "$health_response"
else
    echo "❌ 健康检查失败: $health_response"
fi

# 信息接口
echo ""
echo "2. 测试信息接口:"
info_response=$(curl -s http://localhost:8000/info 2>/dev/null)
if echo "$info_response" | grep -q '"name"'; then
    echo "✅ 信息接口通过"
    echo "$info_response"
else
    echo "❌ 信息接口失败: $info_response"
fi

# 设备接口
echo ""
echo "3. 测试设备接口:"
devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null)
if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ 设备接口通过"
    echo "设备数量: $(echo "$devices_response" | grep -o '"total":[0-9]*' | cut -d: -f2)"
else
    echo "❌ 设备接口失败: $devices_response"
fi

# 统计接口
echo ""
echo "4. 测试统计接口:"
stats_response=$(curl -s http://localhost:8000/stats 2>/dev/null)
if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ 统计接口通过"
else
    echo "❌ 统计接口失败: $stats_response"
fi

# 单次绕过
echo ""
echo "5. 测试单次绕过:"
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
fi

# 批量绕过
echo ""
echo "6. 测试批量绕过:"
batch_response=$(curl -s -X POST http://localhost:8000/bypass/batch \
  -H "Content-Type: application/json" \
  -d '{
    "requests": [
      {
        "target_url": "https://httpbin.org/get",
        "method": "GET",
        "strategy": "adaptive"
      }
    ]
  }' 2>/dev/null)

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ 批量绕过API测试成功"
    echo "响应预览:"
    echo "$batch_response" | head -3
else
    echo "❌ 批量绕过API测试失败"
    echo "错误响应:"
    echo "$batch_response"
fi

# 14. 统计测试结果
echo ""
echo "🎯 API测试总结:"
echo "================================"

success_count=0
total_count=6

# 检查各个接口
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ /health - 通过"
    ((success_count++))
else
    echo "❌ /health - 失败"
fi

if echo "$info_response" | grep -q '"name"'; then
    echo "✅ /info - 通过"
    ((success_count++))
else
    echo "❌ /info - 失败"
fi

if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ /devices - 通过"
    ((success_count++))
else
    echo "❌ /devices - 失败"
fi

if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ /stats - 通过"
    ((success_count++))
else
    echo "❌ /stats - 失败"
fi

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ /bypass/single - 通过"
    ((success_count++))
else
    echo "❌ /bypass/single - 失败"
fi

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ /bypass/batch - 通过"
    ((success_count++))
else
    echo "❌ /bypass/batch - 失败"
fi

echo "================================"
echo "通过率: $success_count/$total_count ($(( success_count * 100 / total_count ))%)"

if [ $success_count -eq $total_count ]; then
    echo ""
    echo "🎉 所有API测试通过！服务修复成功！"
    echo "现在可以运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo ""
    echo "⚠️ 部分API测试失败，需要进一步调试"
    echo "查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
