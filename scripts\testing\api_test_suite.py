#!/usr/bin/env python3
"""
星巴克设备指纹绕过系统 - 完整API测试套件
Enhanced API Test Suite for Starbucks Device Fingerprint Bypass System
"""

import asyncio
import aiohttp
import json
import time
import sys
import argparse
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import concurrent.futures
import statistics

class EnhancedAPITester:
    """增强版API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", verbose: bool = False):
        self.base_url = base_url.rstrip('/')
        self.verbose = verbose
        self.session = None
        self.test_results = []
        self.performance_data = []
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=60, connect=10)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': 'API-Test-Suite/1.0'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def log_result(self, test_name: str, success: bool, details: str = "", 
                   response_time: float = 0, status_code: int = 0):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "response_time": response_time,
            "status_code": status_code,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        if self.verbose:
            status = "[成功]" if success else "[失败]"
            print(f"{status} {test_name} - {response_time:.3f}s - {details}")
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> tuple:
        """发送HTTP请求并记录性能数据"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                response_time = time.time() - start_time
                data = await response.json() if response.content_type == 'application/json' else await response.text()
                
                self.performance_data.append({
                    'endpoint': endpoint,
                    'method': method,
                    'response_time': response_time,
                    'status_code': response.status,
                    'timestamp': time.time()
                })
                
                return response.status, data, response_time
        except Exception as e:
            response_time = time.time() - start_time
            return 0, str(e), response_time
    
    async def test_health_check(self) -> bool:
        """健康检查测试"""
        print("[健康检查] 测试服务健康状态...")
        
        status, data, response_time = await self.make_request('GET', '/health')
        
        if status == 200:
            self.log_result("健康检查", True, f"服务正常运行", response_time, status)
            return True
        else:
            self.log_result("健康检查", False, f"服务异常: {data}", response_time, status)
            return False
    
    async def test_api_status(self) -> bool:
        """API状态测试"""
        print("[API状态] 测试API状态接口...")
        
        status, data, response_time = await self.make_request('GET', '/api/status')
        
        if status == 200 and isinstance(data, dict):
            self.log_result("API状态", True, f"API正常", response_time, status)
            return True
        else:
            self.log_result("API状态", False, f"API异常: {data}", response_time, status)
            return False
    
    async def test_device_fingerprint_generation(self) -> bool:
        """设备指纹生成测试"""
        print("[指纹生成] 测试设备指纹生成...")
        
        test_data = {
            "device_id": "test_device_001",
            "strategy": "adaptive"
        }
        
        status, data, response_time = await self.make_request(
            'POST', '/api/fingerprint/generate', 
            json=test_data
        )
        
        if status == 200 and isinstance(data, dict) and 'fingerprint' in data:
            self.log_result("指纹生成", True, f"成功生成指纹", response_time, status)
            return True
        else:
            self.log_result("指纹生成", False, f"生成失败: {data}", response_time, status)
            return False
    
    async def test_bypass_execution(self) -> bool:
        """绕过执行测试"""
        print("[绕过执行] 测试绕过策略执行...")
        
        test_data = {
            "device_id": "test_device_002",
            "strategy": "conservative",
            "target_url": "https://app.starbucks.com/api/test"
        }
        
        status, data, response_time = await self.make_request(
            'POST', '/api/bypass/execute',
            json=test_data
        )
        
        if status == 200 and isinstance(data, dict):
            self.log_result("绕过执行", True, f"执行成功", response_time, status)
            return True
        else:
            self.log_result("绕过执行", False, f"执行失败: {data}", response_time, status)
            return False
    
    async def test_device_management(self) -> bool:
        """设备管理测试"""
        print("[设备管理] 测试设备管理接口...")
        
        # 获取设备列表
        status, data, response_time = await self.make_request('GET', '/api/devices')
        
        if status == 200 and isinstance(data, dict):
            self.log_result("设备列表", True, f"获取成功", response_time, status)
            return True
        else:
            self.log_result("设备列表", False, f"获取失败: {data}", response_time, status)
            return False
    
    async def test_statistics(self) -> bool:
        """统计信息测试"""
        print("[统计信息] 测试统计接口...")
        
        status, data, response_time = await self.make_request('GET', '/api/stats')
        
        if status == 200 and isinstance(data, dict):
            self.log_result("统计信息", True, f"获取成功", response_time, status)
            return True
        else:
            self.log_result("统计信息", False, f"获取失败: {data}", response_time, status)
            return False
    
    async def test_concurrent_requests(self, num_requests: int = 10) -> bool:
        """并发请求测试"""
        print(f"[并发测试] 测试{num_requests}个并发请求...")
        
        async def single_request():
            return await self.make_request('GET', '/api/status')
        
        start_time = time.time()
        tasks = [single_request() for _ in range(num_requests)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        successful = sum(1 for result in results if not isinstance(result, Exception) and result[0] == 200)
        success_rate = successful / num_requests
        
        if success_rate >= 0.9:  # 90%成功率
            self.log_result("并发测试", True, 
                          f"{successful}/{num_requests}成功, 耗时{total_time:.2f}s", 
                          total_time)
            return True
        else:
            self.log_result("并发测试", False, 
                          f"成功率过低: {successful}/{num_requests}", 
                          total_time)
            return False
    
    async def test_performance_benchmark(self) -> bool:
        """性能基准测试"""
        print("[性能测试] 执行性能基准测试...")
        
        # 预热
        for _ in range(5):
            await self.make_request('GET', '/health')
        
        # 基准测试
        test_endpoints = [
            '/health',
            '/api/status',
            '/api/devices',
            '/api/stats'
        ]
        
        benchmark_results = {}
        
        for endpoint in test_endpoints:
            times = []
            for _ in range(10):
                status, data, response_time = await self.make_request('GET', endpoint)
                if status == 200:
                    times.append(response_time)
            
            if times:
                avg_time = statistics.mean(times)
                benchmark_results[endpoint] = avg_time
        
        # 评估性能
        avg_response_time = statistics.mean(benchmark_results.values()) if benchmark_results else float('inf')
        
        if avg_response_time < 1.0:  # 平均响应时间小于1秒
            self.log_result("性能测试", True, 
                          f"平均响应时间: {avg_response_time:.3f}s", 
                          avg_response_time)
            return True
        else:
            self.log_result("性能测试", False, 
                          f"响应时间过长: {avg_response_time:.3f}s", 
                          avg_response_time)
            return False
    
    async def run_all_tests(self, include_performance: bool = True) -> Dict[str, Any]:
        """运行所有测试"""
        print("=" * 60)
        print("星巴克设备指纹绕过系统 - API测试套件")
        print("=" * 60)
        print(f"测试目标: {self.base_url}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 基础功能测试
        basic_tests = [
            self.test_health_check(),
            self.test_api_status(),
            self.test_device_fingerprint_generation(),
            self.test_bypass_execution(),
            self.test_device_management(),
            self.test_statistics()
        ]
        
        basic_results = await asyncio.gather(*basic_tests, return_exceptions=True)
        
        # 性能和并发测试
        if include_performance:
            performance_tests = [
                self.test_concurrent_requests(10),
                self.test_concurrent_requests(30),
                self.test_performance_benchmark()
            ]
            
            performance_results = await asyncio.gather(*performance_tests, return_exceptions=True)
        else:
            performance_results = []
        
        return self.generate_report()
    
    def generate_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        
        # 性能统计
        response_times = [result['response_time'] for result in self.test_results if result['response_time'] > 0]
        avg_response_time = statistics.mean(response_times) if response_times else 0
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'failed_tests': total_tests - successful_tests,
                'success_rate': success_rate,
                'avg_response_time': avg_response_time
            },
            'test_results': self.test_results,
            'performance_data': self.performance_data,
            'timestamp': datetime.now().isoformat()
        }
        
        return report
    
    def print_summary(self):
        """打印测试摘要"""
        report = self.generate_report()
        summary = report['summary']
        
        print("\n" + "=" * 60)
        print("测试结果摘要")
        print("=" * 60)
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功测试: {summary['successful_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1%}")
        print(f"平均响应时间: {summary['avg_response_time']:.3f}s")
        
        # 失败测试详情
        failed_tests = [result for result in self.test_results if not result['success']]
        if failed_tests:
            print("\n失败测试详情:")
            for test in failed_tests:
                print(f"  - {test['test']}: {test['details']}")
        
        print("=" * 60)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='星巴克设备指纹绕过系统API测试套件')
    parser.add_argument('--url', default='http://localhost:8000', help='API服务地址')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--no-performance', action='store_true', help='跳过性能测试')
    parser.add_argument('--output', '-o', help='输出报告文件路径')
    
    args = parser.parse_args()
    
    async with EnhancedAPITester(args.url, args.verbose) as tester:
        report = await tester.run_all_tests(not args.no_performance)
        tester.print_summary()
        
        # 保存报告
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n测试报告已保存到: {args.output}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n[中断] 测试被用户中断")
    except Exception as e:
        print(f"\n\n[错误] 测试执行失败: {e}")
        sys.exit(1)
