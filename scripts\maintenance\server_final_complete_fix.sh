#!/bin/bash

# 最终完整修复 - 补充缺失依赖
echo "🔧 最终完整修复..."

# 1. 停止服务
echo "🛑 停止服务..."
sudo supervisorctl stop starbucks_bypass

# 2. 激活虚拟环境
echo "🔄 激活虚拟环境..."
source /home/<USER>/venv/bin/activate

# 3. 安装缺失的依赖
echo "📦 安装缺失的依赖..."
pip install PyYAML
pip install python-jose[cryptography]
pip install passlib[bcrypt]
pip install jinja2

echo "✅ 补充依赖安装完成"

# 4. 进入项目目录
cd /home/<USER>/apps/starbucks_bypass_tester

# 5. 测试所有模块导入
echo "🔍 测试所有模块导入..."
python3 << 'TEST_IMPORTS'
import sys
sys.path.insert(0, '/home/<USER>/apps/starbucks_bypass_tester/src')

try:
    import yaml
    print('✅ yaml模块导入成功')
except Exception as e:
    print(f'❌ yaml模块导入失败: {e}')
    exit(1)

try:
    from core.api_service import app
    print('✅ API服务模块导入成功')
except Exception as e:
    print(f'❌ API服务模块导入失败: {e}')
    exit(1)

try:
    from core.bypass_engine import BypassEngine
    print('✅ 绕过引擎模块导入成功')
except Exception as e:
    print(f'❌ 绕过引擎模块导入失败: {e}')
    exit(1)

try:
    from core.device_fingerprint_engine import DeviceFingerprintEngine
    print('✅ 设备指纹引擎模块导入成功')
except Exception as e:
    print(f'❌ 设备指纹引擎模块导入失败: {e}')
    exit(1)

print('✅ 所有核心模块导入测试通过')
TEST_IMPORTS

if [ $? -ne 0 ]; then
    echo "❌ 模块导入测试失败"
    deactivate
    exit 1
fi

# 6. 验证API服务文件语法
echo "🔍 验证API服务文件语法..."
cd src
python3 -c "
import ast
with open('core/api_service.py', 'r') as f:
    content = f.read()
try:
    ast.parse(content)
    print('✅ API服务文件语法正确')
except SyntaxError as e:
    print(f'❌ API服务文件语法错误: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 语法验证失败"
    deactivate
    exit 1
fi

# 7. 启动服务
echo "🚀 启动服务..."
cd /home/<USER>/apps/starbucks_bypass_tester
sudo supervisorctl start starbucks_bypass

# 8. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 35

# 9. 检查服务状态
echo "🔍 检查服务状态..."
sudo supervisorctl status starbucks_bypass

# 10. 检查启动日志
echo "📋 检查启动日志..."
echo "=== 输出日志 ==="
tail -20 logs/output.log 2>/dev/null || echo "无输出日志"
echo ""
echo "=== 错误日志 ==="
tail -15 logs/error.log 2>/dev/null || echo "无错误日志"

# 11. 测试健康检查
echo "🧪 测试健康检查..."
health_response=$(curl -s http://localhost:8000/health 2>/dev/null)
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ 健康检查通过"
    echo "$health_response"
else
    echo "❌ 健康检查失败"
    echo "响应: $health_response"
fi

# 12. 测试信息接口
echo "🧪 测试信息接口..."
info_response=$(curl -s http://localhost:8000/info 2>/dev/null)
if echo "$info_response" | grep -q '"name"'; then
    echo "✅ 信息接口测试通过"
    echo "$info_response"
else
    echo "❌ 信息接口测试失败"
    echo "响应: $info_response"
fi

# 13. 测试设备接口
echo "🧪 测试设备接口..."
devices_response=$(curl -s http://localhost:8000/devices 2>/dev/null)
if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ 设备接口测试通过"
    echo "设备数量: $(echo "$devices_response" | grep -o '"total":[0-9]*' | cut -d: -f2)"
else
    echo "❌ 设备接口测试失败"
    echo "响应: $devices_response"
fi

# 14. 测试统计接口
echo "🧪 测试统计接口..."
stats_response=$(curl -s http://localhost:8000/stats 2>/dev/null)
if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ 统计接口测试通过"
else
    echo "❌ 统计接口测试失败"
    echo "响应: $stats_response"
fi

# 15. 测试单次绕过API
echo "🧪 测试单次绕过API..."
bypass_response=$(curl -s -X POST http://localhost:8000/bypass/single \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://httpbin.org/get",
    "method": "GET",
    "strategy": "adaptive"
  }' 2>/dev/null)

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ 单次绕过API测试成功"
    echo "响应预览:"
    echo "$bypass_response" | head -3
else
    echo "❌ 单次绕过API测试失败"
    echo "错误响应:"
    echo "$bypass_response"
fi

# 16. 测试批量绕过API
echo "🧪 测试批量绕过API..."
batch_response=$(curl -s -X POST http://localhost:8000/bypass/batch \
  -H "Content-Type: application/json" \
  -d '{
    "requests": [
      {
        "target_url": "https://httpbin.org/get",
        "method": "GET",
        "strategy": "adaptive"
      }
    ]
  }' 2>/dev/null)

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ 批量绕过API测试成功"
    echo "响应预览:"
    echo "$batch_response" | head -3
else
    echo "❌ 批量绕过API测试失败"
    echo "错误响应:"
    echo "$batch_response"
fi

# 17. 总结测试结果
echo ""
echo "🎯 API测试总结:"
echo "================================"

# 统计成功的接口
success_count=0
total_count=6

# 检查各个接口
if echo "$health_response" | grep -q '"status"'; then
    echo "✅ /health - 通过"
    ((success_count++))
else
    echo "❌ /health - 失败"
fi

if echo "$info_response" | grep -q '"name"'; then
    echo "✅ /info - 通过"
    ((success_count++))
else
    echo "❌ /info - 失败"
fi

if echo "$devices_response" | grep -q '"devices"'; then
    echo "✅ /devices - 通过"
    ((success_count++))
else
    echo "❌ /devices - 失败"
fi

if echo "$stats_response" | grep -q '"service"'; then
    echo "✅ /stats - 通过"
    ((success_count++))
else
    echo "❌ /stats - 失败"
fi

if echo "$bypass_response" | grep -q '"success"'; then
    echo "✅ /bypass/single - 通过"
    ((success_count++))
else
    echo "❌ /bypass/single - 失败"
fi

if echo "$batch_response" | grep -q '"results"'; then
    echo "✅ /bypass/batch - 通过"
    ((success_count++))
else
    echo "❌ /bypass/batch - 失败"
fi

echo "================================"
echo "通过率: $success_count/$total_count ($(( success_count * 100 / total_count ))%)"

if [ $success_count -eq $total_count ]; then
    echo ""
    echo "🎉 所有API测试通过！服务修复成功！"
    echo "现在可以运行完整测试："
    echo "./scripts/run_all_tests.sh"
else
    echo ""
    echo "⚠️ 部分API测试失败，需要进一步调试"
    echo "查看详细日志："
    echo "sudo supervisorctl tail -f starbucks_bypass"
fi

deactivate
